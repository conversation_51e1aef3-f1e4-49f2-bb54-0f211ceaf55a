<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Agent <PERSON></title>
  <link rel="manifest" href="data:application/json,{%22name%22:%22Agent%20Lee%22,%22short_name%22:%22AgentLee%22,%22display%22:%22standalone%22,%22background_color%22:%22transparent%22}">
  
  <style>
    /* Core Styles */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    html, body {
      background: #060818;
      color: #e0e7ff;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      height: 100%;
      width: 100%;
      overflow-x: hidden;
    }

    /* Settings Container */
    .settings-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 30px 20px;
    }

    /* Header */
    .settings-header {
      text-align: center;
      margin-bottom: 40px;
    }

    .settings-title {
      font-size: 36px;
      font-weight: 700;
      background: linear-gradient(135deg, #6C47FF, #00E3FF);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 15px;
    }

    .settings-subtitle {
      font-size: 16px;
      color: #9EA4BD;
    }

    /* Settings Grid */
    .settings-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 25px;
    }

    /* Settings Card */
    .settings-card {
      background: rgba(12, 15, 35, 0.7);
      border-radius: 16px;
      border: 1px solid rgba(108, 71, 255, 0.15);
      overflow: hidden;
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .settings-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3), 0 0 30px rgba(108, 71, 255, 0.2);
      border-color: rgba(108, 71, 255, 0.3);
    }

    .settings-card-header {
      background: rgba(24, 26, 44, 0.8);
      padding: 18px 20px;
      border-bottom: 1px solid rgba(108, 71, 255, 0.15);
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .settings-card-icon {
      font-size: 24px;
      color: #6C47FF;
    }

    .settings-card-body {
      padding: 20px;
      font-size: 14px;
      color: #CCD0E1;
      min-height: 100px;
    }

    /* Back Button */
    .back-button {
      position: fixed;
      top: 20px;
      left: 20px;
      padding: 10px 16px;
      background: rgba(12, 15, 35, 0.7);
      border: 1px solid rgba(108, 71, 255, 0.3);
      border-radius: 8px;
      color: #e0e7ff;
      cursor: pointer;
      font-size: 16px;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;
      z-index: 100;
    }

    .back-button:hover {
      background: rgba(108, 71, 255, 0.2);
      transform: translateY(-2px);
    }

    /* Background */
    .bg-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
    }

    .bg-grid {
      position: absolute;
      width: 200%;
      height: 200%;
      top: -50%;
      left: -50%;
      background-image: 
          linear-gradient(rgba(108, 71, 255, 0.05) 1px, transparent 1px),
          linear-gradient(90deg, rgba(108, 71, 255, 0.05) 1px, transparent 1px);
      background-size: 40px 40px;
      transform: perspective(500px) rotateX(60deg);
      animation: gridMove 20s linear infinite;
      opacity: 0.3;
    }

    @keyframes gridMove {
      0% { background-position: 0 0; }
      100% { background-position: 0 40px; }
    }

    /* Media Queries */
    @media (max-width: 768px) {
      .settings-grid {
        grid-template-columns: 1fr;
      }
      
      .settings-header {
        margin-bottom: 20px;
      }
      
      .settings-title {
        font-size: 28px;
      }
    }
  </style>
</head>
<body>
  <!-- Background -->
  <div class="bg-container">
    <div class="bg-grid"></div>
  </div>

  <!-- Back Button -->
  <a href="index.html" class="back-button">
    ← Back to Agent Lee
  </a>

  <div class="settings-container">
    <div class="settings-header">
      <h1 class="settings-title">Agent Lee Settings</h1>
      <p class="settings-subtitle">Configure and access agent diagnostic tools</p>
    </div>

    <div class="settings-grid">
      <!-- To-Do List -->
      <a class="settings-card" href="uvatg81isz.html" target="_blank">
        <div class="settings-card-header">
          <span class="settings-card-icon">📋</span>
          <span>To-Do List</span>
        </div>
        <div class="settings-card-body">
          View and manage Agent Lee's task queue, priorities and schedules.
        </div>
      </a>

      <!-- Agent Center -->
      <a class="settings-card" href="7qwzcbv3up.html" target="_blank">
        <div class="settings-card-header">
          <span class="settings-card-icon">🤖</span>
          <span>Agent Center</span>
        </div>
        <div class="settings-card-body">
          Monitor and control all agent operations and interactions.
        </div>
      </a>

      <!-- Database Manager -->
      <a class="settings-card" href="b8bwgglju1.html" target="_blank">
        <div class="settings-card-header">
          <span class="settings-card-icon">🗄️</span>
          <span>Database Manager</span>
        </div>
        <div class="settings-card-body">
          Access and manage Agent Lee's database systems and storage.
        </div>
      </a>

      <!-- LLM Brain -->
      <a class="settings-card" href="buhlvyi8so.html" target="_blank">
        <div class="settings-card-header">
          <span class="settings-card-icon">🧠</span>
          <span>LLM Brain</span>
        </div>
        <div class="settings-card-body">
          Configure language model settings and manage prompts.
        </div>
      </a>

      <!-- Workers Center -->
      <a class="settings-card" href="2fjr3w3whb.html" target="_blank">
        <div class="settings-card-header">
          <span class="settings-card-icon">⚙️</span>
          <span>Workers Center</span>
        </div>
        <div class="settings-card-body">
          Monitor and manage all background worker processes.
        </div>
      </a>

      <!-- System Logs -->
      <a class="settings-card" href="hf9nt6zwgw.html" target="_blank">
        <div class="settings-card-header">
          <span class="settings-card-icon">📊</span>
          <span>System Logs</span>
        </div>
        <div class="settings-card-body">
          View detailed system logs and diagnostic information.
        </div>
      </a>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      console.log('Settings page loaded');
    });
  </script>
</body>
</html>