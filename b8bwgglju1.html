<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>CRM Database Dashboard</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Outfit:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700&family=Space+Mono:wght@400;700&display=swap">
  <style>
    :root {
      /* Color Scheme */
      --primary: #6C47FF;
      --primary-light: #8F75FF;
      --primary-dark: #4B31CC;
      --secondary: #00E3FF;
      --secondary-light: #47F2FF;
      --secondary-dark: #00B3CC;
      --accent-1: #FF4499;
      --accent-2: #47FFBE;
      --accent-3: #FFC107;
      --dark: #060818;
      --darker: #030410;
      --card-bg: rgba(12, 15, 35, 0.7);
      --light: #FFFFFF;
      --gray-100: #f8f9fe;
      --gray-200: #ebedf5;
      --gray-300: #CCD0E1;
      --gray-400: #9EA4BD;
      --gray-500: #73799C;
      --gray-600: #5A6081;
      --gray-700: #3B3F56;
      --gray-800: #23263B;
      --status-success: #00FFB3;
      --status-warning: #FFE14C;
      --status-error: #FF4C6F;
      
      /* Database Type Colors */
      --db-indexeddb: #6C47FF;
      --db-sqljs: #00E3FF;
      --db-duckdb: #FF4499;
      --db-vector: #47FFBE;
      --db-cache: #FFC107;
      
      /* UI Elements */
      --border-radius-sm: 10px;
      --border-radius-md: 16px;
      --border-radius-lg: 24px;
      --border-radius-xl: 32px;
      --border-radius-pill: 100px;
      --card-shadow: 0 20px 60px rgba(3, 4, 16, 0.4);
      --card-glow: 0 0 40px rgba(108, 71, 255, 0.2);
      --transition: all 0.4s cubic-bezier(0.17, 0.84, 0.44, 1);
    }
    
    /* Reset and Base Styles */
    *, *::before, *::after {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    @keyframes gradientFlow {
      0% {
        background-position: 0% 50%;
      }
      50% {
        background-position: 100% 50%;
      }
      100% {
        background-position: 0% 50%;
      }
    }
    
    body {
      font-family: 'Outfit', sans-serif;
      background: var(--darker);
      color: var(--light);
      min-height: 100vh;
      overflow-x: hidden;
      line-height: 1.6;
      position: relative;
      scroll-behavior: smooth;
      perspective: 1000px;
    }
    
    body::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: url('images/abstract_digital_data_visualization_with.png');
      background-size: cover;
      background-position: center;
      opacity: 0.3;
      z-index: -2;
    }
    
    body::after {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: 
        radial-gradient(circle at 20% 20%, rgba(108, 71, 255, 0.12), transparent 40%),
        radial-gradient(circle at 80% 30%, rgba(0, 227, 255, 0.12), transparent 40%),
        radial-gradient(circle at 50% 80%, rgba(255, 68, 153, 0.12), transparent 40%);
      z-index: -1;
    }
    
    h1, h2, h3, h4, h5, h6 {
      font-family: 'Poppins', sans-serif;
      font-weight: 700;
      letter-spacing: -0.03em;
      line-height: 1.2;
    }
    
    code, pre {
      font-family: 'Space Mono', monospace;
    }
    
    /* Scrollbar styling */
    ::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
    
    ::-webkit-scrollbar-track {
      background: rgba(35, 38, 59, 0.7);
      border-radius: 10px;
    }
    
    ::-webkit-scrollbar-thumb {
      background: rgba(108, 71, 255, 0.7);
      border-radius: 10px;
    }
    
    ::-webkit-scrollbar-thumb:hover {
      background: rgba(108, 71, 255, 0.9);
    }
    
    /* Animated 3D Grid Background */
    .grid-bg {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
      transform-style: preserve-3d;
      perspective: 1000px;
    }
    
    .grid-bg::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: 
        linear-gradient(rgba(6, 8, 24, 0) 95%, rgba(108, 71, 255, 0.3) 100%),
        linear-gradient(90deg, rgba(6, 8, 24, 0) 95%, rgba(0, 227, 255, 0.3) 100%);
      background-size: 40px 40px;
      transform: rotateX(60deg) translateZ(-100px) scale(3);
      transform-origin: center;
      animation: grid-move 30s linear infinite;
      opacity: 0.2;
    }
    
    @keyframes grid-move {
      0% {
        background-position: 0 0;
      }
      100% {
        background-position: 40px 40px;
      }
    }
    
    /* Floating particles */
    .particles-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      pointer-events: none;
      z-index: -1;
    }
    
    .particle {
      position: absolute;
      border-radius: 50%;
      filter: blur(8px);
      opacity: 0.4;
      mix-blend-mode: screen;
    }
    
    /* Main Header */
    .main-header {
      position: relative;
      z-index: 100;
      padding: 1.2rem 2rem;
      margin: 1.5rem 2rem;
      border-radius: var(--border-radius-lg);
      display: flex;
      justify-content: space-between;
      align-items: center;
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      background: rgba(12, 15, 35, 0.5);
      border: 1px solid rgba(108, 71, 255, 0.2);
      box-shadow: 0 15px 40px rgba(3, 4, 16, 0.5);
      transform-style: preserve-3d;
    }
    
    .main-header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: var(--border-radius-lg);
      background: linear-gradient(45deg, rgba(108, 71, 255, 0.1), rgba(0, 227, 255, 0.1));
      z-index: -1;
    }
    
    .brand {
      display: flex;
      align-items: center;
      gap: 1rem;
      transform: translateZ(20px);
    }
    
    .logo {
      width: 50px;
      height: 50px;
      background: linear-gradient(135deg, var(--primary), var(--secondary));
      border-radius: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 800;
      font-size: 22px;
      color: white;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      box-shadow: 0 10px 25px rgba(108, 71, 255, 0.4);
      position: relative;
      overflow: hidden;
    }
    
    .logo::before {
      content: '';
      position: absolute;
      top: 0;
      left: -50%;
      width: 200%;
      height: 100%;
      background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.2), 
        transparent);
      transform: skewX(-25deg);
      animation: shine 4s infinite;
    }
    
    @keyframes shine {
      0% { left: -100%; }
      50%, 100% { left: 100%; }
    }
    
    .brand-name {
      font-weight: 800;
      font-size: 1.8rem;
      background: linear-gradient(to right, var(--primary), var(--secondary));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      position: relative;
      text-shadow: 0 5px 15px rgba(108, 71, 255, 0.3);
    }
    
    .brand-name::after {
      content: 'Agent Lee\'s DB';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to right, var(--primary), var(--secondary));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      opacity: 0.3;
      filter: blur(8px);
    }
    
    .connection-badge {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 0.6rem 1.2rem;
      background: rgba(6, 8, 24, 0.4);
      border-radius: var(--border-radius-pill);
      border: 1px solid rgba(108, 71, 255, 0.3);
      transition: var(--transition);
      box-shadow: 0 10px 20px rgba(3, 4, 16, 0.2);
      transform: translateZ(20px);
    }
    
    .connection-badge:hover {
      background: rgba(108, 71, 255, 0.15);
      border: 1px solid rgba(108, 71, 255, 0.5);
    }
    
    .connection-indicator {
      width: 12px;
      height: 12px;
      border-radius: 50%;
    }
    
    .connection-connected .connection-indicator {
      background: var(--status-success);
      box-shadow: 0 0 15px var(--status-success);
    }
    
    .connection-connecting .connection-indicator {
      background: var(--status-warning);
      box-shadow: 0 0 15px var(--status-warning);
      animation: pulse 1.5s infinite;
    }
    
    .connection-error .connection-indicator {
      background: var(--status-error);
      box-shadow: 0 0 15px var(--status-error);
    }
    
    .connection-text {
      font-size: 0.9rem;
      font-weight: 600;
    }
    
    @keyframes pulse {
      0% { opacity: 1; transform: scale(1); }
      50% { opacity: 0.6; transform: scale(1.4); }
      100% { opacity: 1; transform: scale(1); }
    }
    
    .header-actions {
      display: flex;
      align-items: center;
      gap: 1.5rem;
      transform: translateZ(20px);
    }
    
    .theme-toggle {
      width: 48px;
      height: 48px;
      border-radius: var(--border-radius-md);
      background: rgba(6, 8, 24, 0.4);
      border: 1px solid rgba(108, 71, 255, 0.3);
      color: var(--light);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: var(--transition);
      font-size: 1.2rem;
    }
    
    .theme-toggle:hover {
      background: rgba(108, 71, 255, 0.15);
      box-shadow: 0 10px 20px rgba(108, 71, 255, 0.2);
    }
    
    .header-metrics {
      display: flex;
      align-items: center;
      gap: 1rem;
    }
    
    .metric-pill {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 0.6rem 1.2rem;
      background: rgba(6, 8, 24, 0.4);
      border-radius: var(--border-radius-pill);
      border: 1px solid rgba(108, 71, 255, 0.3);
      font-size: 0.9rem;
      font-weight: 600;
      color: var(--gray-300);
      transition: var(--transition);
      position: relative;
      overflow: hidden;
    }
    
    .metric-pill:hover {
      background: rgba(108, 71, 255, 0.15);
      box-shadow: 0 10px 20px rgba(108, 71, 255, 0.2);
    }
    
    .metric-pill::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.1), 
        transparent);
      transition: 0.5s;
    }
    
    .metric-pill:hover::before {
      left: 100%;
    }
    
    .metric-pill-icon {
      font-size: 1.1rem;
      background: linear-gradient(135deg, var(--primary), var(--secondary));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    
    .metric-pill span {
      color: var(--light);
      font-weight: 700;
    }
    
    /* Hero Section */
    .hero-section {
      padding: 4rem 2rem 2rem;
      text-align: center;
      position: relative;
      overflow: hidden;
    }
    
    .hero-title {
      font-size: 4rem;
      font-weight: 800;
      margin-bottom: 1.5rem;
      background: linear-gradient(to right, var(--primary), var(--secondary), var(--accent-1));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      position: relative;
      display: inline-block;
      transform: translateZ(30px);
      text-shadow: 0 10px 30px rgba(108, 71, 255, 0.3);
    }
    
    .hero-title::after {
      content: 'Multi-Database Command Center';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to right, var(--primary), var(--secondary), var(--accent-1));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      opacity: 0.2;
      filter: blur(10px);
    }
    
    .hero-subtitle {
      font-size: 1.3rem;
      color: var(--gray-300);
      max-width: 700px;
      margin: 0 auto 3rem;
      position: relative;
      transform: translateZ(20px);
    }
    
    /* Action Buttons */
    .action-buttons {
      display: flex;
      justify-content: center;
      gap: 1.5rem;
      margin-bottom: 3rem;
      flex-wrap: wrap;
      position: relative;
      transform: translateZ(20px);
    }
    
    .button {
      padding: 0.9rem 2rem;
      border-radius: var(--border-radius-pill);
      font-weight: 600;
      font-size: 1rem;
      display: inline-flex;
      align-items: center;
      gap: 0.8rem;
      cursor: pointer;
      transition: var(--transition);
      border: none;
      position: relative;
      overflow: hidden;
      transform-style: preserve-3d;
    }
    
    .button::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(180deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
      opacity: 0;
      transition: var(--transition);
      z-index: 1;
    }
    
    .button:hover {
      box-shadow: 0 15px 30px rgba(108, 71, 255, 0.3);
    }
    
    .button:hover::before {
      opacity: 1;
    }
    
    .button:active {
      opacity: 0.9;
    }
    
    .button-primary {
      background: linear-gradient(135deg, var(--primary), var(--primary-dark));
      color: white;
      box-shadow: 0 10px 25px rgba(108, 71, 255, 0.4);
    }
    
    .button-primary::after {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(45deg, 
        rgba(255, 255, 255, 0) 30%, 
        rgba(255, 255, 255, 0.1) 50%, 
        rgba(255, 255, 255, 0) 70%);
      transform: rotate(30deg);
      animation: shine-button 6s infinite;
    }
    
    @keyframes shine-button {
      0% { transform: rotate(30deg) translateX(-300%); }
      30%, 100% { transform: rotate(30deg) translateX(300%); }
    }
    
    .button-secondary {
      background: rgba(12, 15, 35, 0.6);
      color: var(--light);
      border: 1px solid rgba(108, 71, 255, 0.3);
    }
    
    .button-secondary:hover {
      background: rgba(108, 71, 255, 0.15);
      border: 1px solid rgba(108, 71, 255, 0.5);
    }
    
    .button-icon {
      font-size: 1.2rem;
      z-index: 2;
    }
    
    .button-text {
      z-index: 2;
    }
    
    .button:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }
    
    /* Dashboard Layout */
    .dashboard {
      padding: 0 2rem 2rem;
      display: grid;
      grid-template-columns: repeat(12, 1fr);
      gap: 2rem;
      max-width: 1600px;
      margin: 0 auto;
      position: relative;
      perspective: 1000px;
    }
    
    .dashboard-column {
      display: flex;
      flex-direction: column;
      gap: 2rem;
    }
    
    .dashboard-column.left {
      grid-column: span 5;
    }
    
    .dashboard-column.right {
      grid-column: span 7;
    }
    
    /* Card Components */
    .card {
      border-radius: var(--border-radius-lg);
      background: var(--card-bg);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      border: 1px solid rgba(108, 71, 255, 0.15);
      overflow: hidden;
      box-shadow: var(--card-shadow);
      transition: var(--transition);
      position: relative;
    }
    
    .card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        45deg, 
        transparent, 
        rgba(108, 71, 255, 0.03), 
        transparent);
      z-index: -1;
    }
    
    .card:hover {
      box-shadow: var(--card-shadow), var(--card-glow);
      border-color: rgba(108, 71, 255, 0.3);
    }
    
    .card-header {
      padding: 1.5rem;
      border-bottom: 1px solid rgba(108, 71, 255, 0.1);
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: rgba(12, 15, 35, 0.5);
    }
    
    .card-title {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      font-size: 1.1rem;
      font-weight: 600;
    }
    
    .card-icon {
      width: 38px;
      height: 38px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: var(--border-radius-md);
      background: rgba(108, 71, 255, 0.15);
      font-size: 1.2rem;
      color: var(--primary);
    }
    
    .card-badge {
      padding: 0.3rem 0.9rem;
      border-radius: var(--border-radius-pill);
      background: rgba(108, 71, 255, 0.15);
      font-size: 0.8rem;
      font-weight: 500;
      color: var(--gray-300);
    }
    
    .card-content {
      padding: 1.5rem;
      position: relative;
    }
    
    /* Database Systems */
    .db-systems {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      gap: 1.5rem;
    }
    
    .pentagon-container {
      margin-bottom: 2rem;
      display: flex;
      align-items: flex-start;
      gap: 1.5rem;
    }
    
    .pentagon-visualization {
      flex: 1;
      display: flex;
      justify-content: center;
    }
    
    .pentagon-svg {
      max-width: 100%;
      height: auto;
    }
    
    .cluster-node {
      cursor: pointer;
      transition: r 0.3s ease;
    }
    
    .cluster-node:hover {
      r: 22;
    }
    
    .pentagon-status {
      flex: 1;
      padding: 1rem;
      background: rgba(12, 15, 35, 0.3);
      border-radius: var(--border-radius-md);
      border: 1px solid rgba(108, 71, 255, 0.15);
    }
    
    .cluster-status {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
    }
    
    .status-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.9rem;
    }
    
    .status-dot {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      display: inline-block;
    }
    
    .status-dot.llm {
      background-color: rgba(255, 68, 153, 0.8);
      box-shadow: 0 0 8px rgba(255, 68, 153, 0.6);
    }
    
    .status-dot.agent {
      background-color: rgba(108, 71, 255, 0.8);
      box-shadow: 0 0 8px rgba(108, 71, 255, 0.6);
    }
    
    .status-dot.worker {
      background-color: rgba(255, 193, 7, 0.8);
      box-shadow: 0 0 8px rgba(255, 193, 7, 0.6);
    }
    
    .status-dot.todo {
      background-color: rgba(71, 255, 190, 0.8);
      box-shadow: 0 0 8px rgba(71, 255, 190, 0.6);
    }
    
    .status-dot.meta {
      background-color: rgba(0, 227, 255, 0.8);
      box-shadow: 0 0 8px rgba(0, 227, 255, 0.6);
    }
    
    .db-clusters {
      display: flex;
      flex-direction: column;
      gap: 2rem;
    }
    
    .db-cluster {
      padding: 1.5rem;
      border-radius: var(--border-radius-md);
      background: rgba(12, 15, 35, 0.3);
      border: 1px solid rgba(108, 71, 255, 0.15);
      transition: var(--transition);
      position: relative;
      overflow: hidden;
    }
    
    .db-cluster.llm-memory {
      border-top: 3px solid rgba(255, 68, 153, 0.8);
    }
    
    .db-cluster.agent-center {
      border-top: 3px solid rgba(108, 71, 255, 0.8);
    }
    
    .cluster-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1.2rem;
    }
    
    .cluster-title {
      display: flex;
      align-items: center;
      gap: 0.75rem;
    }
    
    .cluster-icon {
      font-size: 1.3rem;
    }
    
    .cluster-title h3 {
      font-size: 1.2rem;
      font-weight: 600;
    }
    
    .cluster-badge {
      padding: 0.3rem 0.8rem;
      border-radius: var(--border-radius-pill);
      background: rgba(108, 71, 255, 0.15);
      font-size: 0.8rem;
      font-weight: 600;
      color: var(--gray-300);
    }
    
    .cluster-databases {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }
    
    .db-item {
      display: flex;
      align-items: center;
      gap: 1rem;
      padding: 0.8rem;
      background: rgba(12, 15, 35, 0.4);
      border-radius: var(--border-radius-sm);
      border: 1px solid rgba(108, 71, 255, 0.1);
      transition: var(--transition);
    }
    
    .db-item:hover {
      background: rgba(12, 15, 35, 0.6);
      border-color: rgba(108, 71, 255, 0.2);
    }
    
    .db-icon {
      width: 40px;
      height: 40px;
      border-radius: var(--border-radius-sm);
      background: rgba(108, 71, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.3rem;
    }
    
    .db-info {
      flex: 1;
    }
    
    .db-name {
      font-size: 0.95rem;
      font-weight: 600;
    }
    
    .db-type {
      font-size: 0.8rem;
      color: var(--gray-400);
    }
    
    .db-status {
      width: 12px;
      height: 12px;
      border-radius: 50%;
    }
    
    .db-status.active {
      background-color: var(--status-success);
      box-shadow: 0 0 8px var(--status-success);
      animation: pulse-status 2s infinite;
    }
    
    .db-system {
      padding: 1.5rem;
      border-radius: var(--border-radius-md);
      background: rgba(12, 15, 35, 0.3);
      border: 1px solid rgba(108, 71, 255, 0.15);
      transition: var(--transition);
      position: relative;
      overflow: hidden;
    }
    
    .db-system::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 4px;
      background: var(--db-indexeddb);
      transition: var(--transition);
    }
    
    .db-system:hover {
      background: rgba(12, 15, 35, 0.6);
      border-color: rgba(108, 71, 255, 0.3);
      box-shadow: 0 20px 40px rgba(3, 4, 16, 0.4);
    }
    
    .db-system:hover::before {
      height: 6px;
    }
    
    .db-system.indexed::before { background: var(--db-indexeddb); }
    .db-system.sql::before { background: var(--db-sqljs); }
    .db-system.duck::before { background: var(--db-duckdb); }
    .db-system.vector::before { background: var(--db-vector); }
    .db-system.cache::before { background: var(--db-cache); }
    
    .db-system-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 1.5rem;
    }
    
    .db-system-title h3 {
      font-size: 1.3rem;
      margin-bottom: 0.3rem;
      background: linear-gradient(to right, var(--light), var(--gray-300));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    
    .db-system-type {
      font-size: 0.9rem;
      color: var(--gray-400);
    }
    
    .db-system-agent {
      margin-top: 0.8rem;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.4rem 0.8rem;
      border-radius: var(--border-radius-pill);
      background: rgba(108, 71, 255, 0.1);
      font-size: 0.8rem;
      font-weight: 500;
      border: 1px solid rgba(108, 71, 255, 0.2);
    }
    
    .db-system-agent span {
      opacity: 0.9;
    }
    
    .db-system-status {
      width: 42px;
      height: 42px;
      border-radius: 50%;
      background: rgba(0, 255, 179, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      border: 1px solid rgba(0, 255, 179, 0.3);
    }
    
    .db-system-status::before {
      content: '';
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: var(--status-success);
      box-shadow: 0 0 15px var(--status-success);
    }
    
    .db-system-metrics {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 1.2rem;
      margin-top: 1.5rem;
    }
    
    .db-metric {
      padding: 1rem;
      border-radius: var(--border-radius-sm);
      background: rgba(12, 15, 35, 0.3);
      border: 1px solid rgba(108, 71, 255, 0.1);
      transition: var(--transition);
    }
    
    .db-metric:hover {
      background: rgba(108, 71, 255, 0.1);
    }
    
    .db-metric-label {
      font-size: 0.8rem;
      color: var(--gray-400);
      margin-bottom: 0.3rem;
    }
    
    .db-metric-value {
      font-size: 1.1rem;
      font-weight: 700;
      background: linear-gradient(to right, var(--light), var(--gray-300));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    
    .db-system-progress {
      margin-top: 1.5rem;
    }
    
    .progress-bar {
      height: 8px;
      border-radius: 4px;
      background: rgba(12, 15, 35, 0.3);
      overflow: hidden;
      margin-top: 0.5rem;
      position: relative;
    }
    
    .progress-fill {
      height: 100%;
      border-radius: 4px;
      background: var(--db-indexeddb);
      transition: width 1s cubic-bezier(0.17, 0.67, 0.32, 1.33);
      position: relative;
    }
    
    .progress-fill::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.3), 
        transparent);
      animation: progress-shine 2s infinite;
    }
    
    @keyframes progress-shine {
      0% { transform: translateX(-100%); }
      100% { transform: translateX(100%); }
    }
    
    .progress-fill.indexed { background: var(--db-indexeddb); }
    .progress-fill.sql { background: var(--db-sqljs); }
    .progress-fill.duck { background: var(--db-duckdb); }
    .progress-fill.vector { background: var(--db-vector); }
    .progress-fill.cache { background: var(--db-cache); }
    
    .progress-label {
      display: flex;
      justify-content: space-between;
      font-size: 0.8rem;
      color: var(--gray-400);
      margin-bottom: 0.5rem;
    }
    
    /* Performance Chart */
    .chart-container {
      width: 100%;
      height: 320px;
      position: relative;
      padding: 0.5rem;
    }
    
    /* Stats Cards */
    .stats-cards {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 1.5rem;
    }
    
    .stat-card {
      padding: 1.8rem 1.5rem;
      border-radius: var(--border-radius-md);
      background: rgba(12, 15, 35, 0.3);
      border: 1px solid rgba(108, 71, 255, 0.15);
      transition: var(--transition);
      position: relative;
      overflow: hidden;
      text-align: center;
    }
    
    .stat-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 4px;
      background: linear-gradient(to right, var(--primary), var(--secondary));
      transition: var(--transition);
    }
    
    .stat-card:hover {
      background: rgba(12, 15, 35, 0.6);
      border-color: rgba(108, 71, 255, 0.3);
      box-shadow: 0 20px 40px rgba(3, 4, 16, 0.4);
    }
    
    .stat-card:hover::before {
      height: 6px;
    }
    
    .stat-value {
      font-size: 2.8rem;
      font-weight: 800;
      margin-bottom: 0.8rem;
      background: linear-gradient(to right, var(--light), var(--gray-300));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      position: relative;
    }
    
    .stat-value::after {
      content: attr(data-value);
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to right, var(--light), var(--gray-300));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      opacity: 0.1;
      filter: blur(8px);
    }
    
    .stat-label {
      font-size: 0.9rem;
      color: var(--gray-400);
      text-transform: uppercase;
      letter-spacing: 1px;
      font-weight: 600;
      position: relative;
    }
    
    /* Database Health Gauge */
    .gauge-container {
      margin: 2.5rem auto;
      width: 220px;
      position: relative;
      transform-style: preserve-3d;
      perspective: 1000px;
    }
    
    .gauge {
      width: 220px;
      height: 110px;
      position: relative;
      overflow: hidden;
      border-radius: 110px 110px 0 0;
      background: rgba(12, 15, 35, 0.4);
      border: 1px solid rgba(108, 71, 255, 0.2);
      transform-style: preserve-3d;
      transform: translateZ(0);
    }
    
    .gauge-fill {
      position: absolute;
      width: 220px;
      height: 220px;
      border-radius: 50%;
      background: conic-gradient(
        var(--status-error) 0%,
        var(--status-warning) 30%,
        var(--status-success) 60%,
        var(--status-warning) 80%,
        var(--status-error) 100%
      );
      bottom: 0;
      transform-origin: center bottom;
      transform: rotate(0.4turn);
      transition: transform 1.5s cubic-bezier(0.34, 1.56, 0.64, 1);
      box-shadow: 0 0 30px rgba(0, 255, 179, 0.2);
    }
    
    .gauge-mask {
      position: absolute;
      width: 180px;
      height: 180px;
      border-radius: 50%;
      background: var(--darker);
      bottom: -90px;
      left: 20px;
      box-shadow: inset 0 5px 15px rgba(3, 4, 16, 0.7);
    }
    
    .gauge-center {
      position: absolute;
      bottom: 0;
      left: 50%;
      width: 12px;
      height: 12px;
      background: var(--light);
      border-radius: 50%;
      transform: translateX(-50%);
      box-shadow: 0 0 10px rgba(255, 255, 255, 0.7);
    }
    
    .gauge-needle {
      position: absolute;
      width: 4px;
      height: 100px;
      background: var(--light);
      bottom: 0;
      left: 50%;
      transform: translateX(-50%) rotate(0deg);
      transform-origin: bottom center;
      transition: transform 1.5s cubic-bezier(0.34, 1.56, 0.64, 1);
      z-index: 2;
      box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
    }
    
    .gauge-needle::before {
      content: '';
      position: absolute;
      width: 14px;
      height: 14px;
      background: linear-gradient(135deg, var(--light), var(--gray-300));
      border-radius: 50%;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      box-shadow: 0 0 15px rgba(255, 255, 255, 0.7);
    }
    
    .gauge-value {
      position: absolute;
      top: 75px;
      left: 0;
      width: 100%;
      text-align: center;
      font-size: 2rem;
      font-weight: 800;
      color: var(--light);
      text-shadow: 0 0 10px rgba(0, 255, 179, 0.5);
      transform-style: preserve-3d;
      transform: translateZ(20px);
    }
    
    .gauge-label {
      margin-top: 1.2rem;
      text-align: center;
      font-size: 0.9rem;
      color: var(--gray-400);
      text-transform: uppercase;
      letter-spacing: 1px;
      font-weight: 600;
    }
    
    /* System Health Status */
    .system-status {
      margin-top: 2.5rem;
      padding: 1.5rem;
      border-radius: var(--border-radius-md);
      background: rgba(12, 15, 35, 0.3);
      border: 1px solid rgba(0, 255, 179, 0.2);
      display: flex;
      align-items: center;
      gap: 1.5rem;
      box-shadow: 0 10px 30px rgba(3, 4, 16, 0.4);
      transform-style: preserve-3d;
      transform: translateZ(0);
      transition: var(--transition);
    }
    
    .system-status:hover {
      background: rgba(0, 255, 179, 0.05);
      border-color: rgba(0, 255, 179, 0.3);
    }
    
    .status-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: rgba(0, 255, 179, 0.1);
      color: var(--status-success);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.8rem;
      border: 1px solid rgba(0, 255, 179, 0.3);
      box-shadow: 0 0 20px rgba(0, 255, 179, 0.2);
      transform: translateZ(15px);
    }
    
    .status-details {
      flex: 1;
      transform: translateZ(10px);
    }
    
    .status-title {
      font-size: 1.2rem;
      font-weight: 700;
      margin-bottom: 0.4rem;
      color: var(--status-success);
    }
    
    .status-message {
      font-size: 0.9rem;
      color: var(--gray-300);
    }
    
    /* Data Structure and Preview */
    .data-section {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1.5rem;
    }
    
    .structure-panel, .preview-panel {
      background: rgba(12, 15, 35, 0.3);
      border-radius: var(--border-radius-md);
      padding: 1.5rem;
      height: 100%;
      border: 1px solid rgba(108, 71, 255, 0.15);
      transition: var(--transition);
    }
    
    .structure-panel:hover, .preview-panel:hover {
      background: rgba(12, 15, 35, 0.5);
      border-color: rgba(108, 71, 255, 0.25);
    }
    
    .structure-panel h3, .preview-panel h3 {
      margin-bottom: 1.2rem;
      font-size: 1.1rem;
      color: var(--gray-300);
    }
    
    .schema-list {
      display: flex;
      flex-direction: column;
      gap: 1.2rem;
    }
    
    .schema-item {
      border-radius: var(--border-radius-sm);
      overflow: hidden;
      border: 1px solid rgba(108, 71, 255, 0.1);
      background: rgba(12, 15, 35, 0.2);
      transition: var(--transition);
    }
    
    .schema-item:hover {
      border-color: rgba(108, 71, 255, 0.3);
      box-shadow: 0 10px 20px rgba(3, 4, 16, 0.3);
      cursor: pointer;
    }
    
    .schema-item.active {
      border-color: rgba(108, 71, 255, 0.5);
      background: rgba(108, 71, 255, 0.1);
    }
    
    .schema-header {
      padding: 0.8rem 1rem;
      background: rgba(108, 71, 255, 0.1);
      font-weight: 600;
      font-size: 0.95rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid rgba(108, 71, 255, 0.1);
    }
    
    .schema-badge {
      padding: 0.25rem 0.6rem;
      border-radius: var(--border-radius-pill);
      background: rgba(108, 71, 255, 0.2);
      font-size: 0.75rem;
      font-weight: 600;
      color: var(--primary-light);
    }
    
    .schema-fields {
      padding: 0.5rem 0;
    }
    
    .field-item {
      padding: 0.6rem 1rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid rgba(108, 71, 255, 0.05);
      transition: var(--transition);
    }
    
    .field-item:hover {
      background: rgba(108, 71, 255, 0.05);
    }
    
    .field-item:last-child {
      border-bottom: none;
    }
    
    .field-name {
      font-family: 'Space Mono', monospace;
      font-size: 0.85rem;
      color: var(--primary-light);
    }
    
    .field-type {
      font-size: 0.75rem;
      color: var(--gray-400);
      background: rgba(12, 15, 35, 0.4);
      padding: 0.25rem 0.6rem;
      border-radius: var(--border-radius-pill);
      border: 1px solid rgba(108, 71, 255, 0.1);
    }
    
    .preview-empty {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 1.5rem;
      color: var(--gray-500);
    }
    
    .preview-empty-icon {
      font-size: 3.5rem;
      opacity: 0.6;
      background: linear-gradient(135deg, var(--primary), var(--secondary));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      animation: spin 3s infinite ease-in-out;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      50% { transform: rotate(180deg); }
      100% { transform: rotate(360deg); }
    }
    
    .data-preview {
      height: 100%;
      overflow: auto;
    }
    
    .data-table {
      width: 100%;
      border-collapse: collapse;
      font-size: 0.85rem;
    }
    
    .data-table th {
      background: rgba(108, 71, 255, 0.1);
      padding: 0.5rem 0.75rem;
      text-align: left;
      border-bottom: 1px solid rgba(108, 71, 255, 0.2);
      color: var(--primary-light);
      position: sticky;
      top: 0;
      z-index: 10;
    }
    
    .data-table td {
      padding: 0.5rem 0.75rem;
      border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    }
    
    .data-table tr:hover {
      background: rgba(108, 71, 255, 0.05);
    }
    
    .data-table tr:nth-child(even) {
      background: rgba(12, 15, 35, 0.2);
    }
    
    /* Operation Logs */
    .logs-container {
      font-family: 'Space Mono', monospace;
      font-size: 0.8rem;
      height: 300px;
      overflow-y: auto;
      padding-right: 0.5rem;
    }
    
    .log-entry {
      padding: 0.85rem 1rem;
      border-radius: var(--border-radius-sm);
      margin-bottom: 0.7rem;
      display: flex;
      background: rgba(12, 15, 35, 0.3);
      border-left: 3px solid transparent;
      transition: var(--transition);
      transform-style: preserve-3d;
      transform: translateZ(0);
    }
    
    .log-entry:hover {
      background: rgba(12, 15, 35, 0.5);
    }
    
    .log-timestamp {
      color: var(--gray-500);
      margin-right: 1rem;
      flex-shrink: 0;
    }
    
    .log-content {
      flex: 1;
    }
    
    .log-operation {
      font-weight: 600;
      margin-right: 0.5rem;
    }
    
    .log-info {
      border-left-color: var(--tertiary);
    }
    
    .log-info .log-operation {
      color: var(--tertiary);
    }
    
    .log-success {
      border-left-color: var(--status-success);
    }
    
    .log-success .log-operation {
      color: var(--status-success);
    }
    
    .log-warning {
      border-left-color: var(--status-warning);
    }
    
    .log-warning .log-operation {
      color: var(--status-warning);
    }
    
    .log-error {
      border-left-color: var(--status-error);
    }
    
    .log-error .log-operation {
      color: var(--status-error);
    }
    
    /* System Metrics */
    .system-metrics {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 1.2rem;
    }
    
    .system-metric {
      padding: 1.2rem;
      border-radius: var(--border-radius-md);
      background: rgba(12, 15, 35, 0.3);
      border: 1px solid rgba(108, 71, 255, 0.15);
      transition: var(--transition);
    }
    
    .system-metric:hover {
      background: rgba(12, 15, 35, 0.5);
      border-color: rgba(108, 71, 255, 0.3);
      box-shadow: 0 10px 20px rgba(3, 4, 16, 0.3);
    }
    
    .system-metric-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    }
    
    .system-metric-label {
      display: flex;
      align-items: center;
      gap: 0.7rem;
      font-weight: 600;
      font-size: 0.95rem;
    }
    
    .system-metric-icon {
      width: 36px;
      height: 36px;
      border-radius: var(--border-radius-sm);
      background: rgba(108, 71, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.1rem;
      color: var(--primary);
      border: 1px solid rgba(108, 71, 255, 0.2);
    }
    
    .system-metric-value {
      font-weight: 700;
      font-size: 1.1rem;
    }
    
    .metric-bar {
      height: 8px;
      border-radius: 4px;
      background: rgba(12, 15, 35, 0.3);
      overflow: hidden;
      margin-top: 0.8rem;
      position: relative;
    }
    
    .metric-fill {
      height: 100%;
      border-radius: 4px;
      transition: width 1s cubic-bezier(0.17, 0.67, 0.32, 1.33);
      position: relative;
    }
    
    .metric-fill::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.3), 
        transparent);
      animation: progress-shine 2s infinite;
    }
    
    .metric-fill.cpu { background: var(--primary); }
    .metric-fill.gpu { background: var(--secondary); }
    .metric-fill.ram { background: var(--tertiary); }
    .metric-fill.network { background: var(--accent-1); }
    
    .metric-details {
      display: flex;
      justify-content: space-between;
      font-size: 0.8rem;
      color: var(--gray-400);
      margin-top: 0.7rem;
    }
    
    /* USB Devices */
    .usb-devices {
      margin-top: 2rem;
    }
    
    .usb-devices-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1.2rem;
    }
    
    .usb-devices-title {
      font-size: 1.1rem;
      font-weight: 600;
      color: var(--gray-300);
    }
    
    .usb-devices-count {
      padding: 0.3rem 0.8rem;
      border-radius: var(--border-radius-pill);
      background: rgba(108, 71, 255, 0.15);
      font-size: 0.8rem;
      font-weight: 600;
      color: var(--primary-light);
    }
    
    .usb-list {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 1.2rem;
      perspective: 1000px;
    }
    
    .usb-device {
      padding: 1.2rem;
      border-radius: var(--border-radius-md);
      background: rgba(12, 15, 35, 0.3);
      border: 1px solid rgba(108, 71, 255, 0.15);
      transition: var(--transition);
    }
    
    .usb-device:hover {
      background: rgba(12, 15, 35, 0.5);
      border-color: rgba(108, 71, 255, 0.3);
      box-shadow: 0 20px 40px rgba(3, 4, 16, 0.4);
    }
    
    .usb-device-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 1rem;
    }
    
    .usb-device-info {
      display: flex;
      align-items: center;
      gap: 0.9rem;
    }
    
    .usb-device-icon {
      width: 48px;
      height: 48px;
      border-radius: var(--border-radius-sm);
      background: rgba(108, 71, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.5rem;
      border: 1px solid rgba(108, 71, 255, 0.2);
      color: var(--primary);
      transform: translateZ(10px);
    }
    
    .usb-device-name {
      font-weight: 600;
      font-size: 1rem;
      margin-bottom: 0.3rem;
    }
    
    .usb-device-type {
      font-size: 0.8rem;
      color: var(--gray-400);
    }
    
    .usb-device-status {
      padding: 0.3rem 0.7rem;
      border-radius: var(--border-radius-pill);
      font-size: 0.75rem;
      font-weight: 600;
      transform: translateZ(15px);
    }
    
    .usb-device-status.connected {
      background: rgba(0, 255, 179, 0.1);
      color: var(--status-success);
      border: 1px solid rgba(0, 255, 179, 0.3);
      box-shadow: 0 0 15px rgba(0, 255, 179, 0.3);
      animation: pulse-status 2s infinite;
    }
    
    @keyframes pulse-status {
      0% { box-shadow: 0 0 10px rgba(0, 255, 179, 0.2); }
      50% { box-shadow: 0 0 20px rgba(0, 255, 179, 0.5); }
      100% { box-shadow: 0 0 10px rgba(0, 255, 179, 0.2); }
    }
    
    /* Diagnostics Styles */
    .diagnostics-container {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
    }
    
    .diagnostics-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .diagnostics-header h3 {
      font-size: 1.2rem;
      font-weight: 600;
      background: linear-gradient(to right, var(--light), var(--gray-300));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    
    .diagnostics-badge {
      padding: 0.3rem 0.8rem;
      border-radius: var(--border-radius-pill);
      background: rgba(255, 68, 153, 0.1);
      font-size: 0.8rem;
      font-weight: 600;
      color: var(--accent-1);
      border: 1px solid rgba(255, 68, 153, 0.2);
    }
    
    .diagnostics-metrics {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
      gap: 1.2rem;
    }
    
    .diagnostics-metric {
      padding: 1rem;
      background: rgba(12, 15, 35, 0.3);
      border-radius: var(--border-radius-md);
      border: 1px solid rgba(108, 71, 255, 0.15);
      transition: var(--transition);
    }
    
    .diagnostics-metric:hover {
      background: rgba(12, 15, 35, 0.4);
      border-color: rgba(108, 71, 255, 0.25);
    }
    
    .metric-header {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.75rem;
    }
    
    .metric-icon {
      font-size: 1.2rem;
    }
    
    .metric-title {
      font-size: 0.9rem;
      font-weight: 600;
    }
    
    .metric-value {
      font-size: 1.6rem;
      font-weight: 700;
      margin-bottom: 0.75rem;
      background: linear-gradient(to right, var(--light), var(--gray-300));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    
    .heartbeat-indicator {
      height: 30px;
      position: relative;
      overflow: hidden;
    }
    
    .heartbeat-line {
      position: absolute;
      top: 50%;
      left: 0;
      width: 100%;
      height: 2px;
      background: linear-gradient(to right, rgba(108, 71, 255, 0.1), rgba(108, 71, 255, 0.1));
      transform: translateY(-50%);
    }
    
    .heartbeat-line::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to right, rgba(0, 255, 179, 0), rgba(0, 255, 179, 1), rgba(0, 255, 179, 0));
      animation: heartbeat 1.5s infinite;
    }
    
    @keyframes heartbeat {
      0% { transform: translateX(-100%); }
      100% { transform: translateX(100%); }
    }
    
    .waveform-visualization {
      height: 30px;
      position: relative;
      overflow: hidden;
    }
    
    .waveform-line {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to right, var(--primary), var(--secondary));
      mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 200'%3E%3Cpath d='M0,100 C150,0 150,200 300,100 C450,0 450,200 600,100 C750,0 750,200 900,100 C1050,0 1050,200 1200,100' stroke='white' stroke-width='2' fill='none'/%3E%3C/svg%3E");
      mask-size: 100% 100%;
      animation: waveform-move 5s linear infinite;
    }
    
    @keyframes waveform-move {
      0% { transform: translateX(0); }
      100% { transform: translateX(-50%); }
    }
    
    .drift-chart {
      height: 20px;
      background: linear-gradient(to right, 
        rgba(0, 255, 179, 0.3) 0%, 
        rgba(0, 255, 179, 0.3) 30%, 
        rgba(255, 225, 76, 0.3) 30%, 
        rgba(255, 225, 76, 0.3) 70%, 
        rgba(255, 76, 111, 0.3) 70%, 
        rgba(255, 76, 111, 0.3) 100%);
      border-radius: 10px;
      position: relative;
    }
    
    .drift-marker {
      position: absolute;
      top: 50%;
      width: 10px;
      height: 10px;
      background: white;
      border-radius: 50%;
      transform: translate(-50%, -50%);
      box-shadow: 0 0 10px white;
    }
    
    .radar-chart-container {
      margin-top: 1rem;
      position: relative;
    }
    
    .radar-legend {
      display: flex;
      justify-content: center;
      gap: 1rem;
      flex-wrap: wrap;
      margin-top: 0.5rem;
    }
    
    .legend-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.8rem;
    }
    
    .legend-color {
      width: 12px;
      height: 12px;
      border-radius: 2px;
    }
    
    .legend-color.llm {
      background: rgba(255, 68, 153, 0.8);
    }
    
    .legend-color.agent {
      background: rgba(108, 71, 255, 0.8);
    }
    
    .legend-color.worker {
      background: rgba(255, 193, 7, 0.8);
    }
    
    .legend-color.todo {
      background: rgba(71, 255, 190, 0.8);
    }
    
    .legend-color.meta {
      background: rgba(0, 227, 255, 0.8);
    }
    
    /* Notepad Metrics Visualization */
    .notepad-metrics-visualization {
      margin-top: 1.5rem;
      padding: 1rem;
      background: rgba(12, 15, 35, 0.2);
      border-radius: var(--border-radius-sm);
      border: 1px solid rgba(108, 71, 255, 0.1);
    }
    
    .metrics-header h4 {
      font-size: 1rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: var(--gray-300);
    }
    
    .metrics-chart {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
    }
    
    .chart-row {
      display: flex;
      align-items: center;
      gap: 1rem;
    }
    
    .chart-label {
      width: 120px;
      font-size: 0.8rem;
      color: var(--gray-400);
    }
    
    .chart-value {
      width: 60px;
      font-size: 0.8rem;
      font-weight: 600;
      text-align: right;
    }
    
    .stacked-bar {
      flex: 1;
      height: 12px;
      background: rgba(12, 15, 35, 0.4);
      border-radius: 6px;
      overflow: hidden;
      display: flex;
    }
    
    .bar-segment.raw {
      background: rgba(255, 68, 153, 0.7);
    }
    
    .bar-segment.structured {
      background: rgba(71, 255, 190, 0.7);
    }
    
    .heartbeat-chart {
      flex: 1;
      height: 12px;
      display: flex;
      align-items: center;
    }
    
    .heartbeat-indicator.active {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: var(--status-success);
      box-shadow: 0 0 8px var(--status-success);
      animation: pulse-status 2s infinite;
    }
    
    .pulse-graph {
      flex: 1;
      height: 20px;
      display: flex;
      align-items: end;
      gap: 2px;
    }
    
    .pulse-bar {
      flex: 1;
      background: linear-gradient(to top, var(--primary), var(--secondary));
      border-radius: 1px;
      min-height: 3px;
      animation: pulse-bar 2s infinite ease-in-out;
    }
    
    .pulse-bar:nth-child(2) { animation-delay: 0.1s; }
    .pulse-bar:nth-child(3) { animation-delay: 0.2s; }
    .pulse-bar:nth-child(4) { animation-delay: 0.3s; }
    .pulse-bar:nth-child(5) { animation-delay: 0.4s; }
    .pulse-bar:nth-child(6) { animation-delay: 0.5s; }
    .pulse-bar:nth-child(7) { animation-delay: 0.6s; }
    
    @keyframes pulse-bar {
      0%, 100% { opacity: 0.5; }
      50% { opacity: 1; }
    }
    
    .usb-device-speed {
      font-size: 0.85rem;
      font-weight: 600;
      padding: 0.3rem 0.7rem;
      border-radius: var(--border-radius-pill);
      background: rgba(108, 71, 255, 0.1);
      margin-top: 0.8rem;
      display: inline-block;
      border: 1px solid rgba(108, 71, 255, 0.2);
      transform: translateZ(5px);
    }
    
    /* Notepad Styles */
    .notepad-container {
      display: flex;
      flex-direction: column;
      height: 400px;
      background: rgba(12, 15, 35, 0.3);
      border-radius: var(--border-radius-md);
      border: 1px solid rgba(108, 71, 255, 0.15);
      overflow: hidden;
    }
    
    .notepad-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      background: rgba(12, 15, 35, 0.5);
      border-bottom: 1px solid rgba(108, 71, 255, 0.1);
    }
    
    .notepad-title {
      font-size: 1rem;
      font-weight: 600;
      color: var(--light);
    }
    
    .notepad-status {
      font-size: 0.75rem;
      color: var(--gray-400);
      margin-top: 0.25rem;
    }
    
    .notepad-actions {
      display: flex;
      gap: 0.5rem;
    }
    
    .notepad-action-btn {
      width: 36px;
      height: 36px;
      border-radius: var(--border-radius-sm);
      background: rgba(108, 71, 255, 0.1);
      border: 1px solid rgba(108, 71, 255, 0.2);
      color: var(--light);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: var(--transition);
      font-size: 1.1rem;
    }
    
    .notepad-action-btn:hover {
      background: rgba(108, 71, 255, 0.2);
      border-color: rgba(108, 71, 255, 0.3);
    }
    
    .notepad-body {
      flex: 1;
      padding: 0;
      overflow: hidden;
    }
    
    .notepad-textarea {
      width: 100%;
      height: 100%;
      padding: 1rem;
      background: rgba(6, 8, 24, 0.3);
      border: none;
      color: var(--light);
      font-family: 'Space Mono', monospace;
      font-size: 0.9rem;
      line-height: 1.6;
      resize: none;
      outline: none;
    }
    
    .notepad-textarea::placeholder {
      color: var(--gray-500);
    }
    
    .notepad-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.75rem 1rem;
      background: rgba(12, 15, 35, 0.5);
      border-top: 1px solid rgba(108, 71, 255, 0.1);
    }
    
    .notepad-metrics {
      display: flex;
      gap: 1rem;
    }
    
    .notepad-metric {
      font-size: 0.75rem;
      color: var(--gray-400);
    }
    
    .notepad-metric-value {
      font-weight: 600;
      color: var(--light);
    }
    
    .notepad-sync-status {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.75rem;
    }
    
    .notepad-sync-status.connected {
      color: var(--status-success);
    }
    
    .notepad-integration-visual {
      margin-top: 1.5rem;
      padding: 1rem;
      background: rgba(12, 15, 35, 0.2);
      border-radius: var(--border-radius-sm);
      border: 1px solid rgba(108, 71, 255, 0.1);
    }
    
    .integration-flow {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 0.5rem;
      margin-bottom: 0.75rem;
    }
    
    .integration-node {
      padding: 0.4rem 0.75rem;
      border-radius: var(--border-radius-sm);
      font-size: 0.8rem;
      font-weight: 600;
      border: 1px solid rgba(108, 71, 255, 0.2);
    }
    
    .integration-arrow {
      display: flex;
      align-items: center;
      color: var(--gray-400);
      font-size: 1rem;
    }
    
    .notepad-node {
      background: rgba(255, 68, 153, 0.1);
      color: var(--accent-1);
    }
    
    .llm-node {
      background: rgba(108, 71, 255, 0.1);
      color: var(--primary);
    }
    
    .db-node {
      background: rgba(71, 255, 190, 0.1);
      color: var(--accent-2);
    }
    
    .todo-node {
      background: rgba(255, 193, 7, 0.1);
      color: var(--accent-3);
    }
    
    .structured-node {
      background: rgba(0, 227, 255, 0.1);
      color: var(--secondary);
    }
    
    .integration-status {
      text-align: center;
      font-size: 0.8rem;
      color: var(--gray-400);
    }
    
    /* Loading Animation */
    .loading-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: var(--darker);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      transition: opacity 0.8s ease, visibility 0.8s ease;
    }
    
    .loading-hidden {
      opacity: 0;
      visibility: hidden;
    }
    
    .loader {
      position: relative;
      width: 150px;
      height: 150px;
      perspective: 1000px;
    }
    
    .loader-cube {
      position: absolute;
      width: 100%;
      height: 100%;
      transform-style: preserve-3d;
      animation: rotate-cube 8s infinite linear;
    }
    
    @keyframes rotate-cube {
      0% { transform: rotateX(0) rotateY(0); }
      25% { transform: rotateX(90deg) rotateY(90deg); }
      50% { transform: rotateX(180deg) rotateY(180deg); }
      75% { transform: rotateX(270deg) rotateY(270deg); }
      100% { transform: rotateX(360deg) rotateY(360deg); }
    }
    
    .cube-face {
      position: absolute;
      width: 100px;
      height: 100px;
      opacity: 0.8;
      border: 2px solid rgba(108, 71, 255, 0.5);
      box-shadow: 0 0 20px rgba(108, 71, 255, 0.4);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 2rem;
      color: var(--light);
      background: rgba(12, 15, 35, 0.7);
      backdrop-filter: blur(5px);
    }
    
    .cube-face:nth-child(1) { transform: translateZ(50px); background: linear-gradient(135deg, rgba(108, 71, 255, 0.2), rgba(0, 0, 0, 0)); }
    .cube-face:nth-child(2) { transform: rotateY(180deg) translateZ(50px); background: linear-gradient(135deg, rgba(0, 227, 255, 0.2), rgba(0, 0, 0, 0)); }
    .cube-face:nth-child(3) { transform: rotateY(90deg) translateZ(50px); background: linear-gradient(135deg, rgba(255, 68, 153, 0.2), rgba(0, 0, 0, 0)); }
    .cube-face:nth-child(4) { transform: rotateY(-90deg) translateZ(50px); background: linear-gradient(135deg, rgba(71, 255, 190, 0.2), rgba(0, 0, 0, 0)); }
    .cube-face:nth-child(5) { transform: rotateX(90deg) translateZ(50px); background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(0, 0, 0, 0)); }
    .cube-face:nth-child(6) { transform: rotateX(-90deg) translateZ(50px); background: linear-gradient(135deg, rgba(108, 71, 255, 0.2), rgba(0, 0, 0, 0)); }
    
    .loading-text {
      margin-top: 2.5rem;
      font-size: 1.2rem;
      font-weight: 600;
      color: var(--light);
      opacity: 0.9;
      letter-spacing: 1px;
      text-shadow: 0 0 10px rgba(108, 71, 255, 0.5);
      animation: pulse-text 2s infinite;
    }
    
    @keyframes pulse-text {
      0%, 100% { opacity: 0.9; }
      50% { opacity: 0.5; }
    }
    
    .loading-progress {
      margin-top: 1.5rem;
      width: 250px;
      height: 6px;
      background: rgba(12, 15, 35, 0.4);
      border-radius: 3px;
      overflow: hidden;
      position: relative;
    }
    
    .loading-progress-fill {
      height: 100%;
      background: linear-gradient(to right, var(--primary), var(--secondary));
      width: 0%;
      animation: loading-progress 3s ease-in-out forwards;
      position: relative;
    }
    
    .loading-progress-fill::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.4), 
        transparent);
      animation: loading-shine 1.5s infinite;
    }
    
    @keyframes loading-shine {
      0% { transform: translateX(-100%); }
      100% { transform: translateX(100%); }
    }
    
    @keyframes loading-progress {
      0% { width: 0%; }
      60% { width: 70%; }
      100% { width: 100%; }
    }
    
    /* Scroll animation for elements */
    .fade-in {
      opacity: 0;
      transform: translateY(30px);
      transition: opacity 0.8s ease, transform 0.8s ease;
    }
    
    .fade-in.visible {
      opacity: 1;
      transform: translateY(0);
    }
    
    /* Responsive Adjustments */
    @media (max-width: 1200px) {
      .dashboard {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }
      
      .dashboard-column.left,
      .dashboard-column.right {
        grid-column: auto;
      }
      
      .data-section {
        grid-template-columns: 1fr;
      }
      
      .stats-cards {
        grid-template-columns: repeat(2, 1fr);
      }
    }
    
    @media (max-width: 768px) {
      .hero-title {
        font-size: 2.8rem;
      }
      
      .main-header {
        flex-direction: column;
        gap: 1.2rem;
        align-items: flex-start;
        margin: 1rem;
        padding: 1rem;
      }
      
      .header-actions {
        width: 100%;
        justify-content: space-between;
      }
      
      .stats-cards {
        grid-template-columns: 1fr;
      }
      
      .system-metrics {
        grid-template-columns: 1fr;
      }
      
      .usb-list {
        grid-template-columns: 1fr;
      }
    }
    
    @media (max-width: 480px) {
      .dashboard {
        padding: 1rem;
      }
      
      .button {
        width: 100%;
        justify-content: center;
      }
      
      .action-buttons {
        flex-direction: column;
        width: 100%;
      }
    }
    
    /* Theme support */
    @media (prefers-color-scheme: light) {
      body.auto-theme {
        /* Light theme variables would go here */
      }
    }
    
    body.light-theme {
      /* Light theme variables would go here */
    }
  </style>
</head>
<body>
  <!-- Loading Animation -->
  <div class="loading-overlay" id="loading-overlay">
    <div class="loader">
      <div class="loader-cube">
        <div class="cube-face">AL</div>
        <div class="cube-face">SQL</div>
        <div class="cube-face">API</div>
        <div class="cube-face">UI</div>
        <div class="cube-face">DEV</div>
        <div class="cube-face">OP</div>
      </div>
    </div>
    <div class="loading-text">Initializing Agent Lee's DB</div>
    <div class="loading-progress">
      <div class="loading-progress-fill"></div>
    </div>
  </div>
  
  <!-- Background Effects -->
  <div class="grid-bg"></div>
  <div class="particles-container" id="particles-container"></div>
  
  <!-- Header -->
  <header class="main-header">
    <div class="brand">
      <div class="logo">AL</div>
      <div class="brand-name">Agent Lee's DB</div>
      <div class="connection-badge connection-connected" id="status-indicator">
        <div class="connection-indicator"></div>
        <div class="connection-text" id="status-text">Connected</div>
      </div>
    </div>
    
    <div class="header-actions">
      <div class="header-metrics">
        <div class="metric-pill">
          <span class="metric-pill-icon">🗄️</span>
          Stores: <span id="store-count">5</span>
        </div>
        <div class="metric-pill">
          <span class="metric-pill-icon">📊</span>
          Records: <span id="record-count">247</span>
        </div>
      </div>
      <button class="theme-toggle" id="theme-toggle">🌙</button>
    </div>
  </header>
  
  <!-- Hero Section with Action Buttons -->
  <section class="hero-section">
    <h1 class="hero-title fade-in">Multi-Database Command Center</h1>
    <p class="hero-subtitle fade-in">Unified management interface for all your database systems</p>
    
    <div class="action-buttons fade-in">
      <button id="backup-btn" class="button button-primary">
        <span class="button-icon">💾</span>
        <span class="button-text">Backup Database</span>
      </button>
      <button id="restore-btn" class="button button-secondary">
        <span class="button-icon">📥</span>
        <span class="button-text">Restore Database</span>
      </button>
      <button id="add-sample-btn" class="button button-secondary">
        <span class="button-icon">➕</span>
        <span class="button-text">Add Sample Data</span>
      </button>
      <button id="clear-btn" class="button button-secondary">
        <span class="button-icon">🗑️</span>
        <span class="button-text">Clear Database</span>
      </button>
    </div>
  </section>
  
  <!-- Main Dashboard -->
  <section class="dashboard">
    <!-- Left Column -->
    <div class="dashboard-column left">
      <!-- Database Systems Card -->
      <div class="card fade-in">
        <div class="card-header">
          <div class="card-title">
            <div class="card-icon">🌐</div>
            <span>Database Systems</span>
          </div>
          <div class="card-badge" id="db-systems-count">13 Databases in 5 Clusters</div>
        </div>
        <div class="card-content">
          <!-- Pentagon Cluster Overview -->
          <div class="pentagon-container">
            <div class="pentagon-visualization">
              <svg class="pentagon-svg" width="300" height="300" viewBox="0 0 300 300">
                <!-- Pentagon outline -->
                <polygon points="150,30 250,115 210,220 90,220 50,115" 
                         fill="rgba(108, 71, 255, 0.05)" 
                         stroke="rgba(108, 71, 255, 0.3)" 
                         stroke-width="2"/>
                
                <!-- Cluster nodes -->
                <circle cx="150" cy="50" r="20" fill="rgba(255, 68, 153, 0.8)" class="cluster-node llm-cluster" data-cluster="LLM Memory"/>
                <circle cx="230" cy="125" r="20" fill="rgba(108, 71, 255, 0.8)" class="cluster-node agent-cluster" data-cluster="Agent Center"/>
                <circle cx="195" cy="205" r="20" fill="rgba(255, 193, 7, 0.8)" class="cluster-node worker-cluster" data-cluster="Worker Engine"/>
                <circle cx="105" cy="205" r="20" fill="rgba(71, 255, 190, 0.8)" class="cluster-node todo-cluster" data-cluster="Todo + Notepad"/>
                <circle cx="70" cy="125" r="20" fill="rgba(0, 227, 255, 0.8)" class="cluster-node meta-cluster" data-cluster="Meta System"/>
                
                <!-- Center Agent Lee -->
                <circle cx="150" cy="150" r="25" fill="rgba(255, 255, 255, 0.9)" class="agent-lee-center"/>
                
                <!-- Connection lines -->
                <line x1="150" y1="150" x2="150" y2="70" stroke="rgba(255, 255, 255, 0.3)" stroke-width="1"/>
                <line x1="150" y1="150" x2="210" y2="145" stroke="rgba(255, 255, 255, 0.3)" stroke-width="1"/>
                <line x1="150" y1="150" x2="175" y2="185" stroke="rgba(255, 255, 255, 0.3)" stroke-width="1"/>
                <line x1="150" y1="150" x2="125" y2="185" stroke="rgba(255, 255, 255, 0.3)" stroke-width="1"/>
                <line x1="150" y1="150" x2="90" y2="145" stroke="rgba(255, 255, 255, 0.3)" stroke-width="1"/>
                
                <!-- Labels -->
                <text x="150" y="35" text-anchor="middle" fill="white" font-size="10">LLM</text>
                <text x="250" y="115" text-anchor="middle" fill="white" font-size="10">AGENT</text>
                <text x="210" y="235" text-anchor="middle" fill="white" font-size="10">WORKER</text>
                <text x="90" y="235" text-anchor="middle" fill="white" font-size="10">TODO</text>
                <text x="50" y="115" text-anchor="middle" fill="white" font-size="10">META</text>
                <text x="150" y="155" text-anchor="middle" fill="black" font-size="12" font-weight="bold">LEE</text>
              </svg>
            </div>
            <div class="pentagon-status">
              <div class="cluster-status">
                <div class="status-item">
                  <span class="status-dot llm"></span>
                  <span>LLM Memory: 3 DBs</span>
                </div>
                <div class="status-item">
                  <span class="status-dot agent"></span>
                  <span>Agent Center: 3 DBs</span>
                </div>
                <div class="status-item">
                  <span class="status-dot worker"></span>
                  <span>Worker Engine: 2 DBs</span>
                </div>
                <div class="status-item">
                  <span class="status-dot todo"></span>
                  <span>Todo + Notepad: 3 DBs</span>
                </div>
                <div class="status-item">
                  <span class="status-dot meta"></span>
                  <span>Meta System: 2 DBs</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="db-clusters">
            <!-- LLM Memory Cluster -->
            <div class="db-cluster llm-memory">
              <div class="cluster-header">
                <div class="cluster-title">
                  <span class="cluster-icon">🧠</span>
                  <h3>LLM Memory Cluster (C1)</h3>
                </div>
                <div class="cluster-badge">3 Databases</div>
              </div>
              <div class="cluster-databases">
                <div class="db-item">
                  <div class="db-icon">💭</div>
                  <div class="db-info">
                    <div class="db-name">llm_prompts.db</div>
                    <div class="db-type">IndexedDB • 15.2MB</div>
                  </div>
                  <div class="db-status active"></div>
                </div>
                <div class="db-item">
                  <div class="db-icon">🔄</div>
                  <div class="db-info">
                    <div class="db-name">llm_vectors.db</div>
                    <div class="db-type">ChromaDB • 248MB</div>
                  </div>
                  <div class="db-status active"></div>
                </div>
                <div class="db-item">
                  <div class="db-icon">📝</div>
                  <div class="db-info">
                    <div class="db-name">llm_responses.db</div>
                    <div class="db-type">SQLite • 32.1MB</div>
                  </div>
                  <div class="db-status active"></div>
                </div>
              </div>
            </div>
            
            <!-- Agent Center Cluster -->
            <div class="db-cluster agent-center">
              <div class="cluster-header">
                <div class="cluster-title">
                  <span class="cluster-icon">🤖</span>
                  <h3>Agent Center Cluster (C2)</h3>
                </div>
                <div class="cluster-badge">3 Databases</div>
              </div>
              <div class="cluster-databases">
                <div class="db-item">
                  <div class="db-icon">👤</div>
                  <div class="db-info">
                    <div class="db-name">agents_profile.db</div>
                    <div class="db-type">SQLite • 18.7MB</div>
                  </div>
                  <div class="db-status active"></div>
                </div>
                <div class="db-item">
                  <div class="db-icon">🔍</div>
                  <div class="db-info">
                    <div class="db-name">agents_diagnostics.db</div>
                    <div class="db-type">IndexedDB • 9.3MB</div>
                  </div>
                  <div class="db-status active"></div>
                </div>
                <div class="db-item">
                  <div class="db-icon">📋</div>
                  <div class="db-info">
                    <div class="db-name">agents_tasks.db</div>
                    <div class="db-type">Dexie.js • 26.8MB</div>
                  </div>
                  <div class="db-status active"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Database Structure Card -->
      <div class="card fade-in">
        <div class="card-header">
          <div class="card-title">
            <div class="card-icon">🗄️</div>
            <span>Database Structure</span>
          </div>
          <button class="theme-toggle" id="refresh-structure-btn">⟳</button>
        </div>
        <div class="card-content">
          <div class="data-section">
            <div class="structure-panel">
              <h3>Table Schema</h3>
              <div class="schema-list">
                <div class="schema-item" data-table="contacts">
                  <div class="schema-header">
                    <span>contacts</span>
                    <span class="schema-badge">42 records</span>
                  </div>
                  <div class="schema-fields">
                    <div class="field-item">
                      <span class="field-name">id</span>
                      <span class="field-type">INT (PK)</span>
                    </div>
                    <div class="field-item">
                      <span class="field-name">name</span>
                      <span class="field-type">VARCHAR(255)</span>
                    </div>
                    <div class="field-item">
                      <span class="field-name">email</span>
                      <span class="field-type">VARCHAR(255) UNIQUE</span>
                    </div>
                    <div class="field-item">
                      <span class="field-name">category</span>
                      <span class="field-type">VARCHAR(50)</span>
                    </div>
                  </div>
                </div>
                
                <div class="schema-item" data-table="tasks">
                  <div class="schema-header">
                    <span>tasks</span>
                    <span class="schema-badge">28 records</span>
                  </div>
                  <div class="schema-fields">
                    <div class="field-item">
                      <span class="field-name">id</span>
                      <span class="field-type">INT (PK)</span>
                    </div>
                    <div class="field-item">
                      <span class="field-name">contactId</span>
                      <span class="field-type">INT (FK)</span>
                    </div>
                    <div class="field-item">
                      <span class="field-name">title</span>
                      <span class="field-type">VARCHAR(255)</span>
                    </div>
                    <div class="field-item">
                      <span class="field-name">status</span>
                      <span class="field-type">VARCHAR(50)</span>
                    </div>
                    <div class="field-item">
                      <span class="field-name">priority</span>
                      <span class="field-type">INT</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="preview-panel">
              <h3>Data Preview</h3>
              <div class="data-preview" id="data-preview">
                <div class="preview-empty">
                  <div class="preview-empty-icon">⏳</div>
                  <p>Click on a table to preview data</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Right Column -->
    <div class="dashboard-column right">
      <!-- Integrated Notepad Card -->
      <div class="card fade-in">
        <div class="card-header">
          <div class="card-title">
            <div class="card-icon">📝</div>
            <span>Integrated Notepad</span>
          </div>
          <div class="card-badge">LLM Connected</div>
        </div>
        <div class="card-content">
          <div class="notepad-container">
            <div class="notepad-header">
              <div class="notepad-info">
                <div class="notepad-title">Unstructured Data Collection</div>
                <div class="notepad-status">Auto-saving • Last saved 2 minutes ago</div>
              </div>
              <div class="notepad-actions">
                <button class="notepad-action-btn" title="Process with LLM">🧠</button>
                <button class="notepad-action-btn" title="Add to Todo List">📋</button>
                <button class="notepad-action-btn" title="Save to Vector DB">💾</button>
              </div>
            </div>
            <div class="notepad-body">
              <textarea class="notepad-textarea" placeholder="Enter your raw, unstructured data here...">Meeting with client on June 5th at 3pm to discuss project requirements.

Key points to address:
- Timeline expectations
- Budget constraints
- Technical specifications
- Resource allocation

Need to prepare slides by tomorrow and coordinate with the design team.

Research links:
https://example.com/research1
https://example.com/research2

#project #meeting #client #planning</textarea>
            </div>
            <div class="notepad-footer">
              <div class="notepad-metrics">
                <div class="notepad-metric">
                  <span class="notepad-metric-label">Characters:</span>
                  <span class="notepad-metric-value">325</span>
                </div>
                <div class="notepad-metric">
                  <span class="notepad-metric-label">Words:</span>
                  <span class="notepad-metric-value">54</span>
                </div>
                <div class="notepad-metric">
                  <span class="notepad-metric-label">Daily Backups:</span>
                  <span class="notepad-metric-value">24</span>
                </div>
              </div>
              <div class="notepad-sync-status connected">
                <span class="notepad-sync-icon">✓</span>
                <span class="notepad-sync-text">Synced with LLM Brain</span>
              </div>
            </div>
            <div class="notepad-metrics-visualization">
              <div class="metrics-header">
                <h4>Notepad Analytics</h4>
              </div>
              <div class="metrics-chart">
                <div class="chart-row">
                  <div class="chart-label">Raw vs Structured:</div>
                  <div class="stacked-bar">
                    <div class="bar-segment raw" style="width: 65%;" title="Raw Data: 65%"></div>
                    <div class="bar-segment structured" style="width: 35%;" title="Structured Data: 35%"></div>
                  </div>
                  <div class="chart-value">65% / 35%</div>
                </div>
                <div class="chart-row">
                  <div class="chart-label">Sync Status:</div>
                  <div class="heartbeat-chart">
                    <div class="heartbeat-indicator active" title="Connected to LLM Brain"></div>
                  </div>
                  <div class="chart-value">Active</div>
                </div>
                <div class="chart-row">
                  <div class="chart-label">Read/Write Ops:</div>
                  <div class="pulse-graph">
                    <div class="pulse-bar" style="height: 30%"></div>
                    <div class="pulse-bar" style="height: 45%"></div>
                    <div class="pulse-bar" style="height: 80%"></div>
                    <div class="pulse-bar" style="height: 60%"></div>
                    <div class="pulse-bar" style="height: 25%"></div>
                    <div class="pulse-bar" style="height: 40%"></div>
                    <div class="pulse-bar" style="height: 70%"></div>
                  </div>
                  <div class="chart-value">23/min</div>
                </div>
              </div>
            </div>
            <div class="notepad-integration-visual">
              <div class="integration-flow">
                <div class="integration-node notepad-node">Notepad</div>
                <div class="integration-arrow right">→</div>
                <div class="integration-node llm-node">LLM Brain</div>
                <div class="integration-arrow right">→</div>
                <div class="integration-node db-node">Vector DB</div>
                <div class="integration-arrow down">↓</div>
                <div class="integration-node todo-node">Todo List</div>
                <div class="integration-arrow left">←</div>
                <div class="integration-node structured-node">Structured Data</div>
                <div class="integration-arrow left">←</div>
              </div>
              <div class="integration-status">Data flow: Unstructured → Processing → Structured</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Performance Chart Card -->
      <div class="card fade-in">
        <div class="card-header">
          <div class="card-title">
            <div class="card-icon">📈</div>
            <span>System Diagnostics</span>
          </div>
          <div class="card-badge">Live Data</div>
        </div>
        <div class="card-content">
          <div class="diagnostics-container">
            <div class="diagnostics-header">
              <h3>Agent Lee Diagnostics Framework™</h3>
              <div class="diagnostics-badge">Tier 3 Global Monitor</div>
            </div>
            
            <div class="diagnostics-metrics">
              <div class="diagnostics-metric">
                <div class="metric-header">
                  <div class="metric-icon">👥</div>
                  <div class="metric-title">Active Agents</div>
                </div>
                <div class="metric-value">87/125</div>
                <div class="heartbeat-indicator">
                  <div class="heartbeat-line"></div>
                </div>
              </div>
              
              <div class="diagnostics-metric">
                <div class="metric-header">
                  <div class="metric-icon">⚙️</div>
                  <div class="metric-title">Worker Status</div>
                </div>
                <div class="metric-value">238/250</div>
                <div class="metric-bar">
                  <div class="metric-fill worker" style="width: 95.2%"></div>
                </div>
              </div>
              
              <div class="diagnostics-metric">
                <div class="metric-header">
                  <div class="metric-icon">🧠</div>
                  <div class="metric-title">LLM Drift</div>
                </div>
                <div class="metric-value">0.12</div>
                <div class="drift-chart">
                  <div class="drift-marker" style="left: 12%"></div>
                  <div class="drift-range safe"></div>
                  <div class="drift-range warning"></div>
                  <div class="drift-range danger"></div>
                </div>
              </div>
              
              <div class="diagnostics-metric">
                <div class="metric-header">
                  <div class="metric-icon">⚡</div>
                  <div class="metric-title">Ping Spread</div>
                </div>
                <div class="metric-value">24ms</div>
                <div class="waveform-visualization">
                  <div class="waveform-line"></div>
                </div>
              </div>
            </div>
            
            <div class="radar-chart-container">
              <canvas id="radar-chart" height="250"></canvas>
              <div class="radar-legend">
                <div class="legend-item">
                  <span class="legend-color llm"></span>
                  <span>LLM Memory</span>
                </div>
                <div class="legend-item">
                  <span class="legend-color agent"></span>
                  <span>Agent Center</span>
                </div>
                <div class="legend-item">
                  <span class="legend-color worker"></span>
                  <span>Worker Engine</span>
                </div>
                <div class="legend-item">
                  <span class="legend-color todo"></span>
                  <span>Todo + Notepad</span>
                </div>
                <div class="legend-item">
                  <span class="legend-color meta"></span>
                  <span>Meta System</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Performance Metrics -->
      <div class="card fade-in">
        <div class="card-header">
          <div class="card-title">
            <div class="card-icon">📊</div>
            <span>Performance Metrics</span>
          </div>
        </div>
        <div class="card-content">
          <div class="stats-cards">
            <div class="stat-card">
              <div class="stat-value" id="op-count" data-value="3,247">3,247</div>
              <div class="stat-label">Operations</div>
            </div>
            <div class="stat-card">
              <div class="stat-value" id="avg-time" data-value="18ms">18ms</div>
              <div class="stat-label">Avg Response</div>
            </div>
            <div class="stat-card">
              <div class="stat-value" id="last-backup" data-value="10:42">10:42</div>
              <div class="stat-label">Last Backup</div>
            </div>
          </div>
          
          <div class="gauge-container">
            <div class="gauge">
              <div class="gauge-fill"></div>
              <div class="gauge-mask"></div>
              <div class="gauge-needle" id="gauge-needle" style="transform: translateX(-50%) rotate(72deg)"></div>
              <div class="gauge-center"></div>
            </div>
            <div class="gauge-value" id="gauge-value">96%</div>
            <div class="gauge-label">Database Health</div>
          </div>
          
          <div class="system-status">
            <div class="status-icon">✓</div>
            <div class="status-details">
              <div class="status-title">System Status: Operational</div>
              <div class="status-message">All database services are running normally</div>
            </div>
          </div>
          
          <!-- System Performance Metrics -->
          <h3 style="margin: 2rem 0 1rem; font-size: 1.1rem;">System Performance</h3>
          <div class="system-metrics">
            <!-- CPU Usage -->
            <div class="system-metric">
              <div class="system-metric-header">
                <div class="system-metric-label">
                  <div class="system-metric-icon">💻</div>
                  CPU Usage
                </div>
                <div class="system-metric-value">78%</div>
              </div>
              <div class="metric-bar">
                <div class="metric-fill cpu" style="width: 78%"></div>
              </div>
              <div class="metric-details">
                <div>4 cores @ 3.2GHz</div>
                <div>Temp: 65°C</div>
              </div>
            </div>
            
            <!-- GPU Usage -->
            <div class="system-metric">
              <div class="system-metric-header">
                <div class="system-metric-label">
                  <div class="system-metric-icon">🎮</div>
                  GPU Usage
                </div>
                <div class="system-metric-value">42%</div>
              </div>
              <div class="metric-bar">
                <div class="metric-fill gpu" style="width: 42%"></div>
              </div>
              <div class="metric-details">
                <div>NVIDIA RTX 3080</div>
                <div>VRAM: 4.2GB/10GB</div>
              </div>
            </div>
            
            <!-- RAM Usage -->
            <div class="system-metric">
              <div class="system-metric-header">
                <div class="system-metric-label">
                  <div class="system-metric-icon">📊</div>
                  RAM Usage
                </div>
                <div class="system-metric-value">65%</div>
              </div>
              <div class="metric-bar">
                <div class="metric-fill ram" style="width: 65%"></div>
              </div>
              <div class="metric-details">
                <div>10.4GB/16GB</div>
                <div>2933MHz DDR4</div>
              </div>
            </div>
            
            <!-- Internet Connection -->
            <div class="system-metric">
              <div class="system-metric-header">
                <div class="system-metric-label">
                  <div class="system-metric-icon">🌐</div>
                  Network
                </div>
                <div class="system-metric-value">125 Mbps</div>
              </div>
              <div class="metric-bar">
                <div class="metric-fill network" style="width: 85%"></div>
              </div>
              <div class="metric-details">
                <div>Ping: 24ms</div>
                <div>Upload: 18 Mbps</div>
              </div>
            </div>
          </div>
          
          <!-- USB Devices -->
          <div class="usb-devices">
            <div class="usb-devices-header">
              <div class="usb-devices-title">Connected USB Devices</div>
              <div class="usb-devices-count">17 Devices</div>
            </div>
            
            <div class="usb-list">
              <!-- Camera -->
              <div class="usb-device">
                <div class="usb-device-header">
                  <div class="usb-device-info">
                    <div class="usb-device-icon">📷</div>
                    <div>
                      <div class="usb-device-name">Logitech C920 HD Pro</div>
                      <div class="usb-device-type">Webcam • Port 3.0</div>
                    </div>
                  </div>
                  <div class="usb-device-status connected">Connected</div>
                </div>
                <div class="usb-device-speed">1080p/30fps</div>
              </div>
              
              <!-- Microphone -->
              <div class="usb-device">
                <div class="usb-device-header">
                  <div class="usb-device-info">
                    <div class="usb-device-icon">🎤</div>
                    <div>
                      <div class="usb-device-name">Blue Yeti X</div>
                      <div class="usb-device-type">USB Microphone • Port 3.0</div>
                    </div>
                  </div>
                  <div class="usb-device-status connected">Connected</div>
                </div>
                <div class="usb-device-speed">96 kHz/24-bit</div>
              </div>
              
              <!-- External SSD -->
              <div class="usb-device">
                <div class="usb-device-header">
                  <div class="usb-device-info">
                    <div class="usb-device-icon">💾</div>
                    <div>
                      <div class="usb-device-name">Samsung T7 Shield</div>
                      <div class="usb-device-type">External SSD • Port 3.2</div>
                    </div>
                  </div>
                  <div class="usb-device-status connected">Connected</div>
                </div>
                <div class="usb-device-speed">1050 MB/s</div>
              </div>
              
              <!-- Keyboard -->
              <div class="usb-device">
                <div class="usb-device-header">
                  <div class="usb-device-info">
                    <div class="usb-device-icon">⌨️</div>
                    <div>
                      <div class="usb-device-name">Keychron K8 Pro</div>
                      <div class="usb-device-type">Mechanical Keyboard • Port 3.0</div>
                    </div>
                  </div>
                  <div class="usb-device-status connected">Connected</div>
                </div>
                <div class="usb-device-speed">1000Hz polling rate</div>
              </div>
              
              <!-- Mouse -->
              <div class="usb-device">
                <div class="usb-device-header">
                  <div class="usb-device-info">
                    <div class="usb-device-icon">🖱️</div>
                    <div>
                      <div class="usb-device-name">Logitech MX Master 3</div>
                      <div class="usb-device-type">Wireless Mouse • Port 2.0</div>
                    </div>
                  </div>
                  <div class="usb-device-status connected">Connected</div>
                </div>
                <div class="usb-device-speed">4000 DPI</div>
              </div>
              
              <!-- Graphics Tablet -->
              <div class="usb-device">
                <div class="usb-device-header">
                  <div class="usb-device-info">
                    <div class="usb-device-icon">🎨</div>
                    <div>
                      <div class="usb-device-name">Wacom Intuos Pro</div>
                      <div class="usb-device-type">Graphics Tablet • Port 3.0</div>
                    </div>
                  </div>
                  <div class="usb-device-status connected">Connected</div>
                </div>
                <div class="usb-device-speed">8192 pressure levels</div>
              </div>
              
              <!-- External HDD -->
              <div class="usb-device">
                <div class="usb-device-header">
                  <div class="usb-device-info">
                    <div class="usb-device-icon">🖴</div>
                    <div>
                      <div class="usb-device-name">WD Elements 4TB</div>
                      <div class="usb-device-type">External HDD • Port 3.0</div>
                    </div>
                  </div>
                  <div class="usb-device-status connected">Connected</div>
                </div>
                <div class="usb-device-speed">180 MB/s</div>
              </div>
              
              <!-- USB Hub -->
              <div class="usb-device">
                <div class="usb-device-header">
                  <div class="usb-device-info">
                    <div class="usb-device-icon">🔌</div>
                    <div>
                      <div class="usb-device-name">Anker 7-Port USB 3.0</div>
                      <div class="usb-device-type">USB Hub • Port 3.0</div>
                    </div>
                  </div>
                  <div class="usb-device-status connected">Connected</div>
                </div>
                <div class="usb-device-speed">5 Gbps</div>
              </div>
              
              <!-- Audio Interface -->
              <div class="usb-device">
                <div class="usb-device-header">
                  <div class="usb-device-info">
                    <div class="usb-device-icon">🔊</div>
                    <div>
                      <div class="usb-device-name">Focusrite Scarlett 2i2</div>
                      <div class="usb-device-type">Audio Interface • Port 3.0</div>
                    </div>
                  </div>
                  <div class="usb-device-status connected">Connected</div>
                </div>
                <div class="usb-device-speed">192 kHz/24-bit</div>
              </div>
              
              <!-- MIDI Controller -->
              <div class="usb-device">
                <div class="usb-device-header">
                  <div class="usb-device-info">
                    <div class="usb-device-icon">🎹</div>
                    <div>
                      <div class="usb-device-name">Akai MPK Mini MK3</div>
                      <div class="usb-device-type">MIDI Controller • Port 2.0</div>
                    </div>
                  </div>
                  <div class="usb-device-status connected">Connected</div>
                </div>
                <div class="usb-device-speed">25 mini keys</div>
              </div>
              
              <!-- USB Flash Drive -->
              <div class="usb-device">
                <div class="usb-device-header">
                  <div class="usb-device-info">
                    <div class="usb-device-icon">📁</div>
                    <div>
                      <div class="usb-device-name">SanDisk Ultra 128GB</div>
                      <div class="usb-device-type">Flash Drive • Port 3.1</div>
                    </div>
                  </div>
                  <div class="usb-device-status connected">Connected</div>
                </div>
                <div class="usb-device-speed">400 MB/s</div>
              </div>
              
              <!-- USB Card Reader -->
              <div class="usb-device">
                <div class="usb-device-header">
                  <div class="usb-device-info">
                    <div class="usb-device-icon">💳</div>
                    <div>
                      <div class="usb-device-name">Anker SD Card Reader</div>
                      <div class="usb-device-type">Card Reader • Port 3.0</div>
                    </div>
                  </div>
                  <div class="usb-device-status connected">Connected</div>
                </div>
                <div class="usb-device-speed">UHS-II compatible</div>
              </div>
              
              <!-- USB Headset -->
              <div class="usb-device">
                <div class="usb-device-header">
                  <div class="usb-device-info">
                    <div class="usb-device-icon">🎧</div>
                    <div>
                      <div class="usb-device-name">Corsair HS70 Pro</div>
                      <div class="usb-device-type">Wireless Headset • Port 3.0</div>
                    </div>
                  </div>
                  <div class="usb-device-status connected">Connected</div>
                </div>
                <div class="usb-device-speed">50mm drivers</div>
              </div>
              
              <!-- USB Printer -->
              <div class="usb-device">
                <div class="usb-device-header">
                  <div class="usb-device-info">
                    <div class="usb-device-icon">🖨️</div>
                    <div>
                      <div class="usb-device-name">Epson EcoTank ET-4760</div>
                      <div class="usb-device-type">Printer • Port 2.0</div>
                    </div>
                  </div>
                  <div class="usb-device-status connected">Connected</div>
                </div>
                <div class="usb-device-speed">15 ppm</div>
              </div>
              
              <!-- USB Scanner -->
              <div class="usb-device">
                <div class="usb-device-header">
                  <div class="usb-device-info">
                    <div class="usb-device-icon">📠</div>
                    <div>
                      <div class="usb-device-name">Epson Perfection V600</div>
                      <div class="usb-device-type">Scanner • Port 2.0</div>
                    </div>
                  </div>
                  <div class="usb-device-status connected">Connected</div>
                </div>
                <div class="usb-device-speed">6400 dpi</div>
              </div>
              
              <!-- USB Game Controller -->
              <div class="usb-device">
                <div class="usb-device-header">
                  <div class="usb-device-info">
                    <div class="usb-device-icon">🎮</div>
                    <div>
                      <div class="usb-device-name">Xbox Elite Series 2</div>
                      <div class="usb-device-type">Game Controller • Port 3.0</div>
                    </div>
                  </div>
                  <div class="usb-device-status connected">Connected</div>
                </div>
                <div class="usb-device-speed">40-hour battery life</div>
              </div>
              
              <!-- USB Docking Station -->
              <div class="usb-device">
                <div class="usb-device-header">
                  <div class="usb-device-info">
                    <div class="usb-device-icon">🖥️</div>
                    <div>
                      <div class="usb-device-name">CalDigit TS3 Plus</div>
                      <div class="usb-device-type">Thunderbolt Dock • Port 3.1</div>
                    </div>
                  </div>
                  <div class="usb-device-status connected">Connected</div>
                </div>
                <div class="usb-device-speed">40 Gbps</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Operation Logs Card -->
      <div class="card fade-in">
        <div class="card-header">
          <div class="card-title">
            <div class="card-icon">📋</div>
            <span>Operation Logs</span>
          </div>
          <div class="card-badge">Live Feed</div>
        </div>
        <div class="card-content">
          <div class="logs-container" id="db-logs">
            <div class="log-entry log-success">
              <div class="log-timestamp">[10:42:15]</div>
              <div class="log-content">
                <span class="log-operation">BackupDatabase:</span> Database backed up successfully (1.2MB)
              </div>
            </div>
            <div class="log-entry log-info">
              <div class="log-timestamp">[10:41:30]</div>
              <div class="log-content">
                <span class="log-operation">IntegrityCheck:</span> Verified structure for contacts store
              </div>
            </div>
            <div class="log-entry log-info">
              <div class="log-timestamp">[10:41:28]</div>
              <div class="log-content">
                <span class="log-operation">IntegrityCheck:</span> Verified structure for tasks store
              </div>
            </div>
            <div class="log-entry log-warning">
              <div class="log-timestamp">[10:41:05]</div>
              <div class="log-content">
                <span class="log-operation">Connection:</span> Latency spike detected on DuckDB connection
              </div>
            </div>
            <div class="log-entry log-error">
              <div class="log-timestamp">[10:40:45]</div>
              <div class="log-content">
                <span class="log-operation">Error:</span> Failed to sync VectorStoreDB: timeout
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script>
    // Sample data for data preview
    const sampleData = {
      contacts: [
        { id: 1, name: "John Smith", email: "<EMAIL>", category: "Client" },
        { id: 2, name: "Sarah Johnson", email: "<EMAIL>", category: "Partner" },
        { id: 3, name: "Michael Davis", email: "<EMAIL>", category: "Client" },
        { id: 4, name: "Emma Wilson", email: "<EMAIL>", category: "Vendor" },
        { id: 5, name: "James Rodriguez", email: "<EMAIL>", category: "Client" },
        { id: 6, name: "Linda Garcia", email: "<EMAIL>", category: "Partner" },
        { id: 7, name: "Robert Martinez", email: "<EMAIL>", category: "Vendor" },
        { id: 8, name: "Patricia Lee", email: "<EMAIL>", category: "Client" },
        { id: 9, name: "David Wilson", email: "<EMAIL>", category: "Partner" },
        { id: 10, name: "Elizabeth Taylor", email: "<EMAIL>", category: "Client" }
      ],
      tasks: [
        { id: 1, contactId: 1, title: "Review contract", status: "Pending", priority: 2 },
        { id: 2, contactId: 1, title: "Schedule meeting", status: "Completed", priority: 1 },
        { id: 3, contactId: 2, title: "Send proposal", status: "In Progress", priority: 3 },
        { id: 4, contactId: 3, title: "Follow up call", status: "Pending", priority: 2 },
        { id: 5, contactId: 4, title: "Request for information", status: "Completed", priority: 1 },
        { id: 6, contactId: 5, title: "Prepare presentation", status: "In Progress", priority: 3 },
        { id: 7, contactId: 6, title: "Invoice processing", status: "Pending", priority: 2 },
        { id: 8, contactId: 7, title: "Update contact details", status: "Completed", priority: 1 },
        { id: 9, contactId: 8, title: "Send welcome package", status: "In Progress", priority: 2 },
        { id: 10, contactId: 9, title: "Quarterly review", status: "Pending", priority: 3 }
      ]
    };
    
    // Wait for DOM to be fully loaded
    document.addEventListener('DOMContentLoaded', function() {
      // Create and animate particles
      createParticles();
      
      // Hide loading overlay after animation
      setTimeout(() => {
        const loadingOverlay = document.getElementById('loading-overlay');
        loadingOverlay.classList.add('loading-hidden');
      }, 3500);
      
      // Initialize fade-in animations
      const fadeElements = document.querySelectorAll('.fade-in');
      
      const fadeInObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            setTimeout(() => {
              entry.target.classList.add('visible');
            }, 100);
            fadeInObserver.unobserve(entry.target);
          }
        });
      }, {
        threshold: 0.1
      });
      
      fadeElements.forEach(element => {
        fadeInObserver.observe(element);
      });
      
      // Add click event listeners to schema items
      const schemaItems = document.querySelectorAll('.schema-item');
      const dataPreview = document.getElementById('data-preview');
      
      schemaItems.forEach(item => {
        item.addEventListener('click', function() {
          // Remove active class from all schema items
          schemaItems.forEach(i => i.classList.remove('active'));
          // Add active class to clicked item
          this.classList.add('active');
          
          // Get the table name from data attribute
          const tableName = this.getAttribute('data-table');
          
          // Create table with data based on selected table
          if (tableName && sampleData[tableName]) {
            showTableData(tableName, sampleData[tableName]);
          }
        });
      });
      
      function showTableData(tableName, data) {
        if (!data || data.length === 0) return;
        
        // Get column names from the first object
        const columns = Object.keys(data[0]);
        
        // Create table HTML
        let tableHTML = `<table class="data-table">
          <thead>
            <tr>
              ${columns.map(col => `<th>${col}</th>`).join('')}
            </tr>
          </thead>
          <tbody>`;
        
        // Add data rows
        data.forEach(row => {
          tableHTML += `<tr>
            ${columns.map(col => `<td>${row[col]}</td>`).join('')}
          </tr>`;
        });
        
        tableHTML += `</tbody></table>`;
        
        // Update preview panel with table
        dataPreview.innerHTML = tableHTML;
        
        // Add animation to the new table
        const tableElement = dataPreview.querySelector('.data-table');
        tableElement.style.opacity = '0';
        setTimeout(() => {
          tableElement.style.transition = 'opacity 0.5s ease';
          tableElement.style.opacity = '1';
        }, 50);
      }
      
      // Auto-click the first schema item to show data immediately
      setTimeout(() => {
        if (schemaItems.length > 0) {
          schemaItems[0].click();
        }
      }, 4000);
      
      // Initialize charts
      Chart.defaults.color = '#9EA4BD';
      Chart.defaults.font.family = 'Outfit, sans-serif';
      
      // Performance chart for metrics
      const ctxPerformance = document.getElementById('performance-chart');
      if (ctxPerformance) {
        const ctx = ctxPerformance.getContext('2d');
        
        // Generate sample data
        const generateTimeLabels = () => {
          const labels = [];
          for (let i = 20; i > 0; i--) {
            const time = new Date();
            time.setMinutes(time.getMinutes() - i);
            labels.push(time.toLocaleTimeString('en-US', { 
              hour: '2-digit', 
              minute: '2-digit',
              hour12: true 
            }));
          }
          return labels;
        };
        
        const generateRandomData = (min, max, count) => {
          const data = [];
          for (let i = 0; i < count; i++) {
            data.push(Math.floor(Math.random() * (max - min + 1)) + min);
          }
          return data;
        };
        
        const timeLabels = generateTimeLabels();
        const responseTimeData = generateRandomData(5, 30, 20);
        const operationsData = generateRandomData(10, 50, 20);
        
        const performanceChart = new Chart(ctx, {
          type: 'line',
          data: {
            labels: timeLabels,
            datasets: [
              {
                label: 'Response Time (ms)',
                data: responseTimeData,
                borderColor: '#6C47FF',
                backgroundColor: 'rgba(108, 71, 255, 0.1)',
                borderWidth: 3,
                pointRadius: 4,
                pointBackgroundColor: '#6C47FF',
                pointBorderColor: 'rgba(108, 71, 255, 0.3)',
                pointBorderWidth: 2,
                pointHoverRadius: 6,
                tension: 0.4,
                fill: true
              },
              {
                label: 'Operations/s',
                data: operationsData,
                borderColor: '#00E3FF',
                backgroundColor: 'rgba(0, 227, 255, 0.1)',
                borderWidth: 3,
                pointRadius: 4,
                pointBackgroundColor: '#00E3FF',
                pointBorderColor: 'rgba(0, 227, 255, 0.3)',
                pointBorderWidth: 2,
                pointHoverRadius: 6,
                tension: 0.4,
                fill: true
              }
            ]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
              mode: 'index',
              intersect: false
            },
            plugins: {
              legend: {
                position: 'top',
                labels: {
                  boxWidth: 12,
                  padding: 15,
                  font: {
                    size: 12,
                    weight: 'bold'
                  }
                }
              },
              tooltip: {
                backgroundColor: 'rgba(12, 15, 35, 0.8)',
                borderColor: 'rgba(108, 71, 255, 0.3)',
                borderWidth: 1,
                padding: 12,
                titleFont: {
                  size: 14,
                  weight: 'bold'
                },
                bodyFont: {
                  size: 13
                },
                displayColors: false,
                caretSize: 8,
                cornerRadius: 8
              }
            },
            scales: {
              x: {
                grid: {
                  color: 'rgba(255, 255, 255, 0.05)',
                  drawBorder: false
                },
                ticks: {
                  maxRotation: 0,
                  font: {
                    size: 10
                  }
                }
              },
              y: {
                grid: {
                  color: 'rgba(255, 255, 255, 0.05)',
                  drawBorder: false
                },
                ticks: {
                  font: {
                    size: 11
                  },
                  padding: 8
                }
              }
            }
          }
        });
        
        // Update chart with random data periodically
        setInterval(() => {
          performanceChart.data.labels.shift();
          const now = new Date();
          performanceChart.data.labels.push(now.toLocaleTimeString('en-US', { 
            hour: '2-digit', 
            minute: '2-digit',
            hour12: true 
          }));
          
          performanceChart.data.datasets.forEach(dataset => {
            dataset.data.shift();
            if (dataset.label.includes('Response')) {
              dataset.data.push(Math.floor(Math.random() * 25) + 5);
            } else {
              dataset.data.push(Math.floor(Math.random() * 40) + 10);
            }
          });
          
          performanceChart.update();
        }, 5000);
      }
      
      // Radar chart for pentagon diagnostics
      const ctxRadar = document.getElementById('radar-chart');
      if (ctxRadar) {
        const radarChart = new Chart(ctxRadar, {
          type: 'radar',
          data: {
            labels: ['Uptime', 'Response Time', 'Memory Usage', 'Query Speed', 'Sync Status'],
            datasets: [
              {
                label: 'LLM Memory',
                data: [95, 75, 82, 88, 92],
                backgroundColor: 'rgba(255, 68, 153, 0.2)',
                borderColor: 'rgba(255, 68, 153, 0.8)',
                pointBackgroundColor: 'rgba(255, 68, 153, 1)',
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: 'rgba(255, 68, 153, 1)'
              },
              {
                label: 'Agent Center',
                data: [98, 85, 78, 90, 85],
                backgroundColor: 'rgba(108, 71, 255, 0.2)',
                borderColor: 'rgba(108, 71, 255, 0.8)',
                pointBackgroundColor: 'rgba(108, 71, 255, 1)',
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: 'rgba(108, 71, 255, 1)'
              },
              {
                label: 'Worker Engine',
                data: [92, 90, 95, 80, 88],
                backgroundColor: 'rgba(255, 193, 7, 0.2)',
                borderColor: 'rgba(255, 193, 7, 0.8)',
                pointBackgroundColor: 'rgba(255, 193, 7, 1)',
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: 'rgba(255, 193, 7, 1)'
              },
              {
                label: 'Todo + Notepad',
                data: [96, 92, 85, 95, 90],
                backgroundColor: 'rgba(71, 255, 190, 0.2)',
                borderColor: 'rgba(71, 255, 190, 0.8)',
                pointBackgroundColor: 'rgba(71, 255, 190, 1)',
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: 'rgba(71, 255, 190, 1)'
              },
              {
                label: 'Meta System',
                data: [99, 88, 92, 85, 97],
                backgroundColor: 'rgba(0, 227, 255, 0.2)',
                borderColor: 'rgba(0, 227, 255, 0.8)',
                pointBackgroundColor: 'rgba(0, 227, 255, 1)',
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: 'rgba(0, 227, 255, 1)'
              }
            ]
          },
          options: {
            elements: {
              line: {
                borderWidth: 2
              }
            },
            plugins: {
              legend: {
                display: false
              }
            },
            scales: {
              r: {
                angleLines: {
                  color: 'rgba(255, 255, 255, 0.1)'
                },
                grid: {
                  color: 'rgba(255, 255, 255, 0.1)'
                },
                pointLabels: {
                  color: 'rgba(255, 255, 255, 0.7)',
                  font: {
                    size: 10
                  }
                },
                ticks: {
                  backdropColor: 'transparent',
                  color: 'rgba(255, 255, 255, 0.5)'
                }
              }
            }
          }
        });
      }
      
      // Create particles
      function createParticles() {
        const container = document.getElementById('particles-container');
        const particleColors = [
          'rgba(108, 71, 255, 0.4)', // Primary
          'rgba(0, 227, 255, 0.4)',  // Secondary
          'rgba(255, 68, 153, 0.4)', // Accent 1
          'rgba(71, 255, 190, 0.4)',  // Accent 2
          'rgba(255, 193, 7, 0.4)'   // Accent 3
        ];
        
        // Create particles
        for (let i = 0; i < 30; i++) {
          const particle = document.createElement('div');
          particle.classList.add('particle');
          
          // Random properties
          const size = Math.random() * 150 + 30; // Size between 30-180px
          const color = particleColors[Math.floor(Math.random() * particleColors.length)];
          
          // Position
          const posX = Math.random() * 100;
          const posY = Math.random() * 100;
          
          // Apply styles
          particle.style.width = `${size}px`;
          particle.style.height = `${size}px`;
          particle.style.background = color;
          particle.style.left = `${posX}%`;
          particle.style.top = `${posY}%`;
          
          // Animation
          const duration = Math.random() * 60 + 30; // 30-90s
          const xMovement = Math.random() * 10 - 5; // -5 to 5
          const yMovement = Math.random() * 10 - 5; // -5 to 5
          
          particle.style.animation = `float ${duration}s infinite linear`;
          particle.style.animationDelay = `-${Math.random() * 60}s`;
          
          // Create keyframes for this specific particle
          const keyframes = `
            @keyframes float {
              0% { transform: translate(0, 0); }
              25% { transform: translate(${xMovement}%, ${yMovement}%); }
              50% { transform: translate(${-yMovement}%, ${xMovement}%); }
              75% { transform: translate(${-xMovement}%, ${-yMovement}%); }
              100% { transform: translate(0, 0); }
            }
          `;
          
          // Add keyframes to document
          const style = document.createElement('style');
          style.textContent = keyframes;
          document.head.appendChild(style);
          
          // Add to container
          container.appendChild(particle);
        }
      }
    });
  </script>
</body>
</html>