<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Agent <PERSON>'s LLM Brain</title>
  <style>
    body {
      background: #0a1525;
      color: #e0f7fa;
      font-family: 'Segoe UI', <PERSON><PERSON>, sans-serif;
      margin: 0;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
    }
    .llm-brain-container {
      margin-top: 48px;
      background: #181f2e;
      border-radius: 16px;
      box-shadow: 0 4px 32px #00b4d880;
      padding: 32px 24px;
      width: 420px;
      max-width: 95vw;
      display: flex;
      flex-direction: column;
      align-items: stretch;
    }
    h1 {
      font-size: 2em;
      margin-bottom: 18px;
      text-align: center;
      letter-spacing: 1px;
    }
    label {
      font-weight: bold;
      margin-bottom: 6px;
      display: block;
    }
    select, textarea {
      font-size: 1em;
      border-radius: 8px;
      border: 1px solid #00b4d8;
      padding: 8px;
      margin-bottom: 12px;
      width: 100%;
      box-sizing: border-box;
    }
    textarea {
      min-height: 80px;
      resize: vertical;
      color: #222;
    }
    .llm-btn {
      background: #1bf7cd;
      color: #0a1525;
      border: none;
      border-radius: 8px;
      padding: 10px 24px;
      font-size: 1.1em;
      font-weight: 600;
      cursor: pointer;
      margin-top: 4px;
      box-shadow: 0 2px 8px #1bf7cd80;
      transition: background 0.2s;
    }
    .llm-btn:hover {
      background: #00b4d8;
    }
    #response {
      margin-top: 18px;
      min-height: 2em;
      font-size: 1.1em;
      white-space: pre-line;
      background: #232b3d;
      border-radius: 8px;
      padding: 12px;
      border: 1px solid #00b4d8;
      color: #e0f7fa;
    }
    .voice-toggle {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 12px;
    }
    .voice-btn {
      background: #48cae4;
      color: #0a1525;
      border: none;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      font-size: 1.3em;
      cursor: pointer;
      box-shadow: 0 2px 8px #00b4d880;
      transition: background 0.2s;
    }
    .voice-btn.active {
      background: #1bf7cd;
      box-shadow: 0 0 16px #1bf7cd;
    }
    .apikey-modal {
      position: fixed;
      top: 0; left: 0; right: 0; bottom: 0;
      background: rgba(10,21,37,0.98);
      z-index: 1000;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #e0f7fa;
      font-size: 1.2em;
    }
    .apikey-modal input {
      font-size: 1em;
      border-radius: 8px;
      border: 1px solid #00b4d8;
      padding: 8px;
      margin-bottom: 12px;
      width: 320px;
      max-width: 90vw;
      box-sizing: border-box;
    }
    .apikey-btn {
      background: #1bf7cd;
      color: #0a1525;
      border: none;
      border-radius: 8px;
      padding: 10px 24px;
      font-size: 1.1em;
      font-weight: 600;
      cursor: pointer;
      margin-top: 4px;
      box-shadow: 0 2px 8px #1bf7cd80;
      transition: background 0.2s;
    }
    .apikey-btn:hover {
      background: #00b4d8;
    }
  </style>
  <script type="module">
    import { GoogleGenerativeAI } from "https://esm.run/@google/generative-ai";

    // --- API Key Modal Logic ---
    function showApiKeyModal() {
      const modal = document.createElement('div');
      modal.className = 'apikey-modal';
      modal.innerHTML = `
        <h2>Enter Gemini API Key</h2>
        <input id="api-key-input" type="password" placeholder="Paste your Google API Key here" autocomplete="off" />
        <button id="save-api-key" class="apikey-btn">Save Key</button>
        <div style="font-size:0.9em; margin-top:10px; color:#aaa;">Your key is stored only in this browser (localStorage).</div>
      `;
      document.body.appendChild(modal);
      document.getElementById('save-api-key').onclick = () => {
        const key = document.getElementById('api-key-input').value.trim();
        if (key) {
          localStorage.setItem('GOOGLE_API_KEY', key);
          document.body.removeChild(modal);
          location.reload();
        }
      };
    }

    let API_KEY = localStorage.getItem('GOOGLE_API_KEY');
    if (!API_KEY || API_KEY === 'PASTE_YOUR_KEY_HERE') {
      window.addEventListener('DOMContentLoaded', showApiKeyModal);
    }

    // --- Main LLM Logic ---
    let genAI;
    let selectedModel = localStorage.getItem("preferredLLM") || "gemini";
    let voiceEnabled = localStorage.getItem("voiceEnabled") === "true";

    async function speak(text) {
      if ('speechSynthesis' in window && voiceEnabled) {
        const utter = new SpeechSynthesisUtterance(text);
        window.speechSynthesis.speak(utter);
      }
    }

    window.addEventListener('DOMContentLoaded', () => {
      API_KEY = localStorage.getItem('GOOGLE_API_KEY') || 'PASTE_YOUR_KEY_HERE';
      genAI = new GoogleGenerativeAI(API_KEY);
      const inputBox = document.getElementById("prompt");
      const outputBox = document.getElementById("response");
      const sendBtn = document.getElementById("send");
      const selector = document.getElementById("llm-selector");
      const voiceBtn = document.getElementById('voice-btn');

      selector.value = selectedModel;
      selector.addEventListener("change", (e) => {
        selectedModel = e.target.value;
        localStorage.setItem("preferredLLM", selectedModel);
      });

      if (voiceBtn) {
        voiceBtn.classList.toggle('active', voiceEnabled);
        voiceBtn.onclick = () => {
          voiceEnabled = !voiceEnabled;
          localStorage.setItem('voiceEnabled', voiceEnabled);
          voiceBtn.classList.toggle('active', voiceEnabled);
        };
      }

      sendBtn.addEventListener("click", async () => {
        const prompt = inputBox.value.trim();
        if (!prompt) return;
        outputBox.innerText = "Thinking...";
        try {
          if (selectedModel === "gemini") {
            const model = genAI.getGenerativeModel({ model: "gemini-pro" });
            const result = await model.generateContent(prompt);
            const text = result.response.text();
            outputBox.innerText = text;
            speak(text);
          } else {
            // Local fallback: call to local Ollama API
            const res = await fetch(`http://localhost:11434/api/generate`, {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                model: selectedModel,
                prompt,
                stream: false
              })
            });
            const data = await res.json();
            outputBox.innerText = data.response || "[No response from local model]";
            speak(data.response || "[No response from local model]");
          }
        } catch (err) {
          outputBox.innerText = "⚠️ Error: " + err.message;
          speak("Error: " + err.message);
        }
      });
    });
  </script>
</head>
<body>
  <div class="llm-brain-container">
    <h1>🧠 Agent Lee’s LLM Brain</h1>
    <label for="llm-selector">Choose LLM:</label>
    <select id="llm-selector" aria-label="Model Selector">
      <option value="gemini">Gemini 2.5 Pro (Cloud)</option>
      <option value="phi3">Phi-3 Mini (Local)</option>
      <option value="mistral">Mistral 7B (Local)</option>
    </select>
    <div class="voice-toggle">
      <span>Voice Output</span>
      <button id="voice-btn" class="voice-btn" title="Toggle Voice Output">🔊</button>
    </div>
    <label for="prompt">Your Prompt:</label>
    <textarea id="prompt" placeholder="Ask something intelligent..."></textarea>
    <button id="send" class="llm-btn">Send</button>
    <div id="response"></div>
  </div>
</body>
</html>
