<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>'s Integrated Workers Center</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Orbitron:wght@400;500;700;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --bg-gradient-1: #0a0e29;
            --bg-gradient-2: #1a1040;
            --bg-gradient-3: #0f0520;
            --primary-color: #00f2ff;
            --primary-glow: rgba(0, 242, 255, 0.6);
            --secondary-color: #9854ff;
            --accent-color: #ff2a6d;
            --accent-glow: rgba(255, 42, 109, 0.6);
            --text-color: #e0e7ff;
            --panel-bg: rgba(16, 20, 40, 0.4);
            --panel-border: rgba(0, 242, 255, 0.15);
            --panel-header: rgba(24, 26, 44, 0.8);
            --chart-blue: #00d1ff; /* Idle */
            --chart-purple: #9854ff; 
            --chart-red: #ff416c; /* Error */
            --chart-yellow: #ffd166; /* Processing */
            --chart-green: #06d6a0; /* Active */
            --shadow: rgba(0, 0, 0, 0.3);
            --card-glow: 0 0 20px rgba(0, 242, 255, 0.15);
            --header-glow: 0 5px 25px rgba(0, 242, 255, 0.15);
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, var(--bg-gradient-1), var(--bg-gradient-2), var(--bg-gradient-3));
            background-size: 400% 400%;
            animation: gradientBG 15s ease infinite;
            color: var(--text-color);
            min-height: 100vh;
            overflow-x: hidden;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        @keyframes gradientBG {
            0% { background-position: 0% 50% }
            50% { background-position: 100% 50% }
            100% { background-position: 0% 50% }
        }

        /* Background Effects */
        .background-effect {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            pointer-events: none;
            z-index: 0;
            overflow: hidden;
        }

        .holographic-grid {
            position: absolute;
            width: 200%;
            height: 200%;
            top: -50%;
            left: -50%;
            background-image: 
                linear-gradient(rgba(0, 242, 255, 0.05) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 242, 255, 0.05) 1px, transparent 1px);
            background-size: 40px 40px;
            transform: perspective(500px) rotateX(60deg);
            animation: gridMove 20s linear infinite;
            opacity: 0.3;
        }

        @keyframes gridMove {
            0% { background-position: 0 0; }
            100% { background-position: 0 40px; }
        }

        .floating-particles {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: var(--primary-color);
            border-radius: 50%;
            box-shadow: 0 0 10px var(--primary-glow);
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% {
                transform: translateY(0) translateX(0);
                opacity: 0;
            }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% {
                transform: translateY(-100vh) translateX(100px);
                opacity: 0;
            }
        }

        /* Header Section */
        .header {
            position: relative;
            z-index: 10;
            padding: 0;
            border-bottom: 1px solid var(--panel-border);
            background: linear-gradient(to right, rgba(10, 14, 41, 0.8), rgba(30, 15, 70, 0.8));
            box-shadow: var(--header-glow);
            backdrop-filter: blur(10px);
            clip-path: polygon(0 0, 100% 0, 100% 85%, 97% 100%, 0 100%);
            margin-bottom: 20px;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 22px 35px;
        }

        .logo-container {
            position: relative;
            display: flex;
            align-items: center;
        }

        .logo {
            font-family: 'Orbitron', sans-serif;
            font-weight: 700;
            font-size: 28px;
            color: var(--primary-color);
            text-shadow: 0 0 15px rgba(0, 242, 255, 0.7);
            position: relative;
            margin-left: 15px;
        }

        .logo-icon {
            position: relative;
            width: 45px;
            height: 45px;
            margin-right: 15px;
        }

        .logo-icon .circle-outer {
            position: absolute;
            width: 100%;
            height: 100%;
            border: 2px solid var(--primary-color);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .logo-icon .circle-inner {
            position: absolute;
            width: 60%;
            height: 60%;
            top: 20%;
            left: 20%;
            border: 1.5px solid var(--secondary-color);
            border-radius: 50%;
            animation: pulse 2s infinite 0.3s;
        }

        .logo-icon .circle-core {
            position: absolute;
            width: 30%;
            height: 30%;
            top: 35%;
            left: 35%;
            background: var(--accent-color);
            border-radius: 50%;
            box-shadow: 0 0 10px var(--accent-glow);
            animation: glow 2s infinite alternate;
        }

        .neon-text {
            color: var(--accent-color);
            text-shadow: 
                0 0 5px rgba(255, 42, 109, 0.5),
                0 0 10px rgba(255, 42, 109, 0.5),
                0 0 15px rgba(255, 42, 109, 0.5);
            font-size: 0.8em;
            vertical-align: middle;
        }

        .status-module {
            display: flex;
            align-items: center;
            background: rgba(16, 20, 40, 0.6);
            padding: 8px 20px;
            border-radius: 30px;
            border: 1px solid var(--panel-border);
            box-shadow: 0 0 15px rgba(0, 242, 255, 0.2);
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 12px;
            background: var(--chart-green); /* Default to green */
            box-shadow: 0 0 10px rgba(52, 168, 83, 0.7);
            animation: pulse 2s infinite;
            transition: background-color 0.5s ease, box-shadow 0.5s ease;
        }
         .status-indicator.warning {
            background: var(--chart-yellow);
            box-shadow: 0 0 10px var(--chart-yellow);
        }
        .status-indicator.error {
            background: var(--chart-red);
            box-shadow: 0 0 10px var(--chart-red);
        }


        /* Main Container */
        .main-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 25px;
            padding: 0 30px 30px;
            position: relative;
            z-index: 1;
            max-width: 1800px;
            margin: 0 auto;
            width: 100%;
        }

        /* Control Bar */
        .control-bar {
            background: var(--panel-bg);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            border: 1px solid var(--panel-border);
            padding: 15px 20px;
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            justify-content: center;
            align-items: center;
            box-shadow: var(--card-glow);
            position: relative;
            overflow: hidden;
        }

        .control-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 242, 255, 0.1), transparent);
            transform: translateX(-100%);
            animation: scanline 8s linear infinite;
            pointer-events: none;
        }

        @keyframes scanline {
            0% { transform: translateX(-100%); }
            50%, 100% { transform: translateX(100%); }
        }

        .control-button {
            background: rgba(14, 22, 45, 0.7);
            border: 1px solid var(--panel-border);
            color: var(--text-color);
            padding: 12px 18px;
            border-radius: 8px;
            font-family: 'Inter', sans-serif;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 8px;
            position: relative;
            overflow: hidden;
        }
        
        #heartbeatCheckBtn {
            background: linear-gradient(135deg, rgba(14, 22, 45, 0.7), rgba(152, 84, 255, 0.3));
            border-color: var(--secondary-color);
            position: relative;
        }
        
        #heartbeatCheckBtn i {
            color: var(--chart-red);
            animation: pulse-heart 1.5s infinite;
        }
        
        @keyframes pulse-heart {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }

        .control-button.primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: #fff;
            font-weight: 600;
        }

        .control-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 14px rgba(0, 0, 0, 0.15), 0 0 10px rgba(0, 242, 255, 0.3);
        }

        /* Main Content Area */
        .content-area {
            display: grid;
            grid-template-columns: 1fr 500px;
            gap: 25px;
            height: calc(100vh - 240px); /* Adjusted to show content properly */
            min-height: 600px; /* Reduced minimum height */
            margin-bottom: 20px; /* Add bottom margin to prevent touching the edge of viewport */
        }


        /* Worker Display Container */
        .worker-display-container {
            background: var(--panel-bg);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            border: 1px solid var(--panel-border);
            overflow: hidden; /* Changed back to hidden */
            box-shadow: var(--card-glow);
            position: relative;
            display: flex;
            flex-direction: column;
            height: 100%; /* Make sure it uses full available height */
            max-height: calc(100vh - 220px); /* Prevent overflow beyond viewport */
        }

        .display-header {
            background: var(--panel-header);
            padding: 16px 20px;
            border-bottom: 1px solid var(--panel-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0; /* Prevent header from shrinking */
        }

        .display-title {
            font-family: 'Orbitron', sans-serif;
            font-size: 18px;
            font-weight: 600;
            color: var(--text-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .display-title i {
            color: var(--primary-color);
        }

        .worker-count {
            background: rgba(0, 0, 0, 0.2);
            color: var(--primary-color);
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-family: 'Orbitron', sans-serif;
            font-weight: 500;
            border: 1px solid var(--panel-border);
        }

        /* Holographic Worker Display */
        .holographic-display {
            flex: 1;
            position: relative;
            background: linear-gradient(45deg, rgba(0, 0, 0, 0.2), rgba(0, 242, 255, 0.02));
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden; /* Changed back to hidden */
            min-height: 350px; /* Reduced for better balance */
            padding-bottom: 0; /* Remove padding to maximize space */
            margin-bottom: 0; /* Remove margin to maximize space */
        }

        .holographic-platform {
            position: absolute;
            width: 300px;
            height: 10px;
            background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
            border-radius: 50%;
            bottom: 120px;
            filter: blur(5px);
            opacity: 0.7;
            box-shadow: 0 0 20px var(--primary-glow);
            transition: background 0.5s ease, box-shadow 0.5s ease;
        }

        .worker-hologram {
            position: relative;
            width: 200px;
            height: 300px;
            display: flex;
            flex-direction: column;
            align-items: center;
            transform-style: preserve-3d;
            transform: translateY(-30px);
            animation: float-hologram 4s ease-in-out infinite;
        }

        @keyframes float-hologram {
            0%, 100% { transform: translateY(-30px); }
            50% { transform: translateY(-40px); }
        }

        .worker-silhouette {
            width: 100px;
            height: 180px;
            background: linear-gradient(to bottom, var(--primary-color), var(--secondary-color));
            clip-path: polygon(
                30% 0%, 70% 0%, 
                70% 10%, 80% 10%, 
                80% 20%, 70% 20%,
                70% 40%, 80% 45%,
                80% 50%, 70% 55%,
                70% 80%, 60% 100%,
                40% 100%, 30% 80%,
                30% 55%, 20% 50%,
                20% 45%, 30% 40%,
                30% 20%, 20% 20%,
                20% 10%, 30% 10%
            );
            opacity: 0.7;
            position: relative;
            margin-bottom: 20px;
            transition: background 0.5s ease, opacity 0.5s ease;
            transform: scale(1.2); /* Slightly larger silhouette */
        }
        .worker-silhouette.status-processing { background: linear-gradient(to bottom, var(--chart-yellow), var(--primary-color)); opacity: 0.8; }
        .worker-silhouette.status-error { background: linear-gradient(to bottom, var(--chart-red), var(--accent-color)); opacity: 0.9; }
        .worker-silhouette.status-idle { background: linear-gradient(to bottom, var(--chart-blue), var(--secondary-color)); opacity: 0.6; }


        .worker-silhouette::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(transparent, var(--primary-color));
            opacity: 0.3;
            animation: pulse-silhouette 2s ease-in-out infinite alternate;
        }

        @keyframes pulse-silhouette {
            0% { opacity: 0.2; }
            100% { opacity: 0.5; }
        }

        .worker-scan-line {
            position: absolute;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
            animation: scan-worker 3s ease-in-out infinite;
            opacity: 0.7;
            filter: blur(2px);
            transition: background 0.5s ease;
        }
        .worker-scan-line.status-processing { background: linear-gradient(90deg, transparent, var(--chart-yellow), transparent); animation-duration: 1.5s; }
        .worker-scan-line.status-error { background: linear-gradient(90deg, transparent, var(--chart-red), transparent); animation-duration: 0.8s; }
        .worker-scan-line.status-idle { background: linear-gradient(90deg, transparent, var(--chart-blue), transparent); animation-duration: 5s; }


        @keyframes scan-worker {
            0%, 100% { top: 0; }
            50% { top: 180px; }
        }

        .worker-name {
            font-family: 'Orbitron', sans-serif;
            font-size: 24px;
            font-weight: 700;
            color: var(--primary-color);
            text-shadow: 0 0 10px var(--primary-glow);
            margin-bottom: 8px;
            text-align: center;
        }

        .worker-path {
            font-family: 'Inter', sans-serif;
            font-size: 15px;
            color: var(--text-color);
            opacity: 0.8;
            margin-bottom: 15px;
            text-align: center;
        }

        .worker-agent {
            font-family: 'Inter', sans-serif;
            font-size: 15px;
            color: var(--text-color);
            opacity: 0.8;
            margin-bottom: 15px;
            text-align: center;
            font-weight: 500;
        }

        .worker-function { /* This will display current task or primary function */
            font-family: 'Inter', sans-serif;
            font-size: 12px;
            color: var(--accent-color);
            background: rgba(0, 0, 0, 0.3);
            padding: 4px 10px;
            border-radius: 10px;
            border: 1px solid var(--accent-color);
            min-height: 28px; /* Ensure it doesn't jump too much */
            display: inline-flex;
            align-items: center;
            justify-content: center;
            max-width: 180px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* Worker Carousel */
        .worker-list-container {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px 20px;
            border-top: 1px solid var(--panel-border);
            overflow: hidden; /* Changed back to hidden */
            position: relative;
            height: 180px; /* Fixed height to ensure consistency */
            min-height: 180px; /* Match the fixed height */
        }

        .worker-list {
            display: flex;
            gap: 15px;
            overflow-x: auto;
            padding: 10px 0;
            scroll-behavior: smooth;
            height: 150px; /* Fixed height */
            align-items: center;
            flex-wrap: nowrap; /* Ensure no wrapping */
        }

        .worker-list::-webkit-scrollbar {
            height: 6px;
        }

        .worker-list::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 3px;
        }

        .worker-list::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 3px;
        }
        
        /* Add hover effect to make scrolling more noticeable */
        .worker-list:hover::-webkit-scrollbar-thumb {
            background: var(--chart-green);
        }

        .worker-card {
            min-width: 120px;
            width: 120px;
            height: 120px;
            background: rgba(10, 14, 30, 0.5);
            border: 1px solid var(--panel-border);
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            flex-shrink: 0;
        }

        .worker-card.active {
            border-color: var(--primary-color);
            box-shadow: 0 0 15px var(--primary-glow);
        }
        
        /* Worker Details Modal */
        .worker-details-modal {
            position: fixed;
            top: 20%;
            left: 50%;
            transform: translateX(-50%);
            z-index: 100;
            width: 80%;
            max-width: 800px;
            background: var(--panel-bg);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            border: 1px solid var(--panel-border);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4), 0 0 20px var(--primary-glow);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            max-height: 80vh;
        }
        
        .worker-details-modal.dragging {
            cursor: grabbing;
            border-color: var(--primary-color);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.6), 0 0 30px var(--primary-glow);
        }

        .worker-details-modal.active {
            opacity: 1;
            visibility: visible;
        }

        .modal-header {
            background: var(--panel-header);
            padding: 15px 20px;
            border-bottom: 1px solid var(--panel-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: move;
            position: relative;
            overflow: hidden;
        }
        
        .modal-header::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 242, 255, 0.1), transparent);
            transform: translateX(-100%);
            animation: scanline 8s linear infinite;
            pointer-events: none;
        }

        .modal-title {
            font-family: 'Orbitron', sans-serif;
            font-size: 18px;
            font-weight: 600;
            color: var(--text-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .modal-title i {
            color: var(--primary-color);
        }

        .modal-controls {
            display: flex;
            gap: 10px;
        }

        .modal-control {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid var(--panel-border);
        }

        .modal-control:hover {
            transform: scale(1.1);
        }

        .modal-control.minimize {
            color: var(--chart-yellow);
        }

        .modal-control.expand {
            color: var(--chart-blue);
        }

        .modal-control.close {
            color: var(--chart-red);
        }

        .modal-content {
            padding: 20px;
            overflow-y: auto;
            flex: 1;
        }

        .modal-minimized {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 200px;
            height: 40px;
            background: var(--panel-header);
            border: 1px solid var(--panel-border);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 15px;
            cursor: pointer;
            z-index: 90;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            opacity: 0;
            visibility: hidden;
        }

        .modal-minimized.active {
            opacity: 1;
            visibility: visible;
        }

        .modal-minimized-title {
            font-family: 'Orbitron', sans-serif;
            font-size: 14px;
            color: var(--text-color);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .modal-expanded {
            width: 90% !important;
            max-width: 1200px !important;
            height: 80vh !important;
        }

        .worker-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .worker-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent, rgba(0, 242, 255, 0.1), transparent);
            transform: translateX(-100%);
            animation: card-sweep 4s infinite;
            pointer-events: none;
        }

        @keyframes card-sweep {
            0% { transform: translateX(-100%); }
            20%, 100% { transform: translateX(100%); }
        }

        .card-avatar {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            background: linear-gradient(135deg, var(--bg-gradient-1), var(--bg-gradient-2));
            border-radius: 50%;
            margin-bottom: 5px;
            position: relative;
            flex-shrink: 0;
        }

        .card-avatar::after {
            content: '';
            position: absolute;
            width: 44px;
            height: 44px;
            border: 1px solid var(--primary-color);
            border-radius: 50%;
            opacity: 0.6;
        }

        .card-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            width: 100%;
        }

        .card-name {
            font-size: 12px;
            font-weight: 600;
            color: var(--text-color);
            text-align: center;
            margin-bottom: 2px;
            max-width: 100px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            line-height: 1.2;
        }

        .card-type {
            font-size: 9px;
            color: var(--secondary-color);
            margin-bottom: 2px;
            font-weight: 500;
        }

        .card-id {
            font-size: 9px;
            color: var(--text-color);
            opacity: 0.7;
            font-family: 'Orbitron', sans-serif;
        }
        
        .card-status-dot {
            width: 14px;
            height: 14px;
            border-radius: 50%;
            position: absolute;
            top: 12px;
            right: 12px;
            border: 1px solid rgba(255,255,255,0.2);
            transition: background-color 0.3s ease, box-shadow 0.3s ease;
            z-index: 10;
        }
        .card-status-dot.status-active { background-color: var(--chart-green); box-shadow: 0 0 6px var(--chart-green); }
        .card-status-dot.status-idle { background-color: var(--chart-blue); box-shadow: 0 0 6px var(--chart-blue); }
        .card-status-dot.status-processing { background-color: var(--chart-yellow); box-shadow: 0 0 6px var(--chart-yellow); animation: pulseStatus 1.5s infinite ease-in-out; }
        .card-status-dot.status-error { background-color: var(--chart-red); box-shadow: 0 0 6px var(--chart-red); }

        @keyframes pulseStatus {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.7; }
        }

        .list-controls {
            position: absolute;
            left: 0;
            width: 100%;
            display: flex;
            justify-content: space-between;
            padding: 0 15px;
            pointer-events: none;
            top: 50%;
            transform: translateY(-50%);
        }

        .list-control {
            width: 40px;
            height: 40px;
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid var(--panel-border);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            pointer-events: auto;
            transition: all 0.3s ease;
            color: var(--primary-color);
        }

        .list-control:hover {
            background: rgba(0, 242, 255, 0.2);
            transform: scale(1.1);
        }

        /* Right Sidebar - Diagnostics */
        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
            height: 100%;
            max-height: 100%;
            overflow: hidden;
        }

        /* Diagnostic Tabs */
        .diagnostic-tabs {
            background: var(--panel-bg);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            border: 1px solid var(--panel-border);
            overflow: hidden;
            box-shadow: var(--card-glow);
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .tabs-header {
            background: var(--panel-header);
            display: flex;
            border-bottom: 1px solid var(--panel-border);
        }

        .tab-button {
            flex: 1;
            padding: 12px 8px;
            background: transparent;
            border: none;
            color: var(--text-color);
            font-size: 11px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            border-right: 1px solid var(--panel-border);
        }

        .tab-button:last-child {
            border-right: none;
        }

        .tab-button.active {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: #fff;
        }

        .tab-button:hover {
            background: rgba(0, 242, 255, 0.1);
        }

        .tab-content {
            padding: 20px;
            flex: 1;
            overflow-y: auto;
            min-height: 300px;
            max-height: calc(100vh - 400px);
        }

        .tab-panel {
            display: none;
        }

        .tab-panel.active {
            display: block;
        }

        /* Diagnostic Elements */
        .diagnostic-section {
            margin-bottom: 20px;
        }

        .diagnostic-title {
            font-family: 'Orbitron', sans-serif;
            font-size: 14px;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .diagnostic-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .metric-card {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 12px;
            border: 1px solid var(--panel-border);
        }

        .metric-label {
            font-size: 11px;
            color: rgba(224, 231, 255, 0.7);
            margin-bottom: 5px;
        }

        .metric-value {
            font-family: 'Orbitron', sans-serif;
            font-size: 16px;
            font-weight: 600;
            color: var(--primary-color);
        }

        /* Chart Containers */
        .chart-container {
            position: relative;
            height: 180px;
            margin-bottom: 15px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            padding: 8px;
            border: 1px solid var(--panel-border);
        }

        .chart-title {
            position: absolute;
            top: 5px;
            left: 10px;
            font-size: 12px;
            color: var(--text-color);
            z-index: 10;
        }

        /* Resource Gauges */
        .gauge-container {
            position: relative;
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .gauge {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: conic-gradient(
                var(--chart-green) 0deg,
                var(--chart-yellow) 180deg,
                var(--chart-red) 270deg,
                transparent 270deg
            );
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .gauge::before {
            content: '';
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: var(--bg-gradient-1);
            position: absolute;
        }

        .gauge-value {
            font-family: 'Orbitron', sans-serif;
            font-size: 14px;
            font-weight: 600;
            color: var(--primary-color);
            z-index: 1;
        }

        /* Log Display */
        .log-container {
            background: rgba(0, 0, 0, 0.4);
            border-radius: 8px;
            padding: 15px;
            border: 1px solid var(--panel-border);
            font-family: 'Courier New', monospace;
            font-size: 11px;
            line-height: 1.4;
            max-height: 200px;
            overflow-y: auto;
        }

        .log-entry {
            margin-bottom: 8px;
            padding: 4px 8px;
            border-radius: 4px;
            border-left: 3px solid var(--chart-blue);
        }

        .log-entry.error {
            border-left-color: var(--chart-red);
            background: rgba(255, 65, 108, 0.1);
        }

        .log-entry.warning {
            border-left-color: var(--chart-yellow);
            background: rgba(255, 209, 102, 0.1);
        }

        .log-entry.success {
            border-left-color: var(--chart-green);
            background: rgba(6, 214, 160, 0.1);
        }

        .log-timestamp {
            color: var(--chart-blue);
            margin-right: 10px;
        }

        /* Heartbeat Indicator */
        .heartbeat {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--chart-green);
            animation: heartbeat 1.5s infinite;
            margin-right: 10px;
        }

        @keyframes heartbeat {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.7; }
        }

        .heartbeat.offline {
            background: var(--chart-red);
            animation: none;
        }

        /* Performance Thermometer */
        .thermometer {
            width: 20px;
            height: 100px;
            background: linear-gradient(to top, 
                var(--chart-green) 0%, 
                var(--chart-yellow) 50%, 
                var(--chart-red) 100%);
            border-radius: 10px;
            position: relative;
            margin: 0 auto;
        }

        .thermometer::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 2px;
            right: 2px;
            height: var(--fill-height, 50%);
            background: rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            transition: height 0.5s ease;
        }

        /* Loading Animation */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--bg-gradient-1), var(--bg-gradient-2), var(--bg-gradient-3));
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 1;
            transition: opacity 0.8s ease;
        }

        .loading-overlay.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .loading-content {
            text-align: center;
        }

        .loading-spinner {
            width: 100px;
            height: 100px;
            border: 3px solid transparent;
            border-top-color: var(--primary-color);
            border-right-color: var(--secondary-color);
            border-bottom-color: var(--accent-color);
            border-radius: 50%;
            animation: spin 2s linear infinite;
            margin: 0 auto 20px;
        }

        .loading-text {
            font-family: 'Orbitron', sans-serif;
            color: var(--primary-color);
            font-size: 24px;
            font-weight: 700;
            text-shadow: 0 0 15px rgba(0, 242, 255, 0.7);
        }

        /* Animations */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.8;
                transform: scale(0.95);
            }
        }

        @keyframes glow {
            0% {
                box-shadow: 0 0 5px var(--accent-glow);
            }
            100% {
                box-shadow: 0 0 20px var(--accent-glow), 0 0 30px var(--accent-glow);
            }
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .content-area {
                grid-template-columns: 1fr;
                gap: 15px;
                height: auto;
                min-height: auto;
            }
            
            .sidebar {
                display: grid;
                grid-template-columns: 1fr;
                gap: 15px;
                height: auto;
            }
            
            .worker-display-container {
                height: auto;
                min-height: 600px;
            }
            
            .diagnostic-tabs {
                height: auto;
                min-height: 600px;
            }
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
                text-align: center;
                padding: 15px;
            }

            .control-bar {
                justify-content: center;
                flex-wrap: wrap;
            }

            .diagnostic-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text">Initializing Agent Lee's System</div>
        </div>
    </div>

    <!-- Background Effects -->
    <div class="background-effect">
        <div class="holographic-grid"></div>
        <div class="floating-particles" id="particles"></div>
    </div>

    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo-container">
                <div class="logo-icon">
                    <div class="circle-outer"></div>
                    <div class="circle-inner"></div>
                    <div class="circle-core"></div>
                </div>
                <div class="logo">Agent Lee's <span class="neon-text">Integrated Workers Center</span></div>
            </div>
            <div class="status-module">
                <span class="status-indicator" id="globalStatusIndicator"></span>
                <span id="globalStatusText">All Systems Operational</span>
            </div>
        </div>
    </header>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Worker Details Modal -->
        <div class="worker-details-modal" id="workerDetailsModal">
            <div class="modal-header" id="modalHeader">
                <div class="modal-title">
                    <i class="fas fa-microchip"></i> <span id="modalTitle">Worker Details</span>
                </div>
                <div class="modal-controls">
                    <div class="modal-control minimize" id="minimizeModal">
                        <i class="fas fa-minus"></i>
                    </div>
                    <div class="modal-control expand" id="expandModal">
                        <i class="fas fa-expand"></i>
                    </div>
                    <div class="modal-control close" id="closeModal">
                        <i class="fas fa-times"></i>
                    </div>
                </div>
            </div>
            <div class="modal-content" id="workerDetailsContent">
                <!-- Worker details will be populated here -->
            </div>
        </div>
        
        <!-- Minimized Modal -->
        <div class="modal-minimized" id="minimizedModal">
            <div class="modal-minimized-title" id="minimizedTitle">Worker Details</div>
            <i class="fas fa-expand"></i>
        </div>
        
        <!-- Control Bar -->
        <div class="control-bar">
            <button class="control-button primary" id="allWorkersBtn">
                <i class="fas fa-th-large"></i> All Workers
            </button>
            <button class="control-button" id="webWorkersBtn">
                <i class="fas fa-globe"></i> Web Workers
            </button>
            <button class="control-button" id="serviceWorkersBtn">
                <i class="fas fa-cogs"></i> Service Workers
            </button>
            <button class="control-button" id="supervisorsBtn">
                <i class="fas fa-user-shield"></i> Supervisors
            </button>
            <button class="control-button" id="runDiagnosticBtn">
                <i class="fas fa-stethoscope"></i> Run Diagnostic
            </button>
            <button class="control-button" id="heartbeatCheckBtn">
                <i class="fas fa-heartbeat"></i> Heartbeat Check
            </button>
        </div>

        <!-- Main Content Area -->
        <div class="content-area">
            <!-- Worker Display Container -->
            <div class="worker-display-container">
                <div class="display-header">
                    <div class="display-title">
                        <i class="fas fa-microchip"></i> Agent Lee's Worker Monitor
                    </div>
                    <div class="worker-count" id="workerCountDisplay">255 Total</div>
                </div>
                
                <div class="holographic-display">
                    <div class="holographic-platform" id="hologramPlatform"></div>
                    <div class="worker-hologram" id="workerHologram">
                        <div class="worker-silhouette" id="hologramSilhouette"></div>
                        <div class="worker-scan-line" id="hologramScanLine"></div>
                        <div class="worker-name" id="hologramName">Quantum</div>
                        <div class="worker-path" id="hologramPath">webworker-house/worker-001</div>
                        <div class="worker-agent" id="hologramAgent">AGENT-002</div>
                        <div class="worker-function" id="hologramFunction">Web Worker</div>
                    </div>
                </div>
                
                <div class="worker-list-container">
                    <div class="list-controls">
                        <div class="list-control" id="prevBtn">
                            <i class="fas fa-chevron-left"></i>
                        </div>
                        <div class="list-control" id="nextBtn">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                    <div class="worker-list" id="workerList">
                        <!-- Worker cards will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Diagnostic Sidebar -->
            <div class="sidebar">
                <div class="diagnostic-tabs">
                    <div class="tabs-header">
                        <button class="tab-button active" data-tab="health">
                            <i class="fas fa-heartbeat"></i><br>Lee's Health
                        </button>
                        <button class="tab-button" data-tab="resources">
                            <i class="fas fa-tachometer-alt"></i><br>Resources
                        </button>
                        <button class="tab-button" data-tab="logs">
                            <i class="fas fa-file-alt"></i><br>Logs
                        </button>
                        <button class="tab-button" data-tab="autonomy">
                            <i class="fas fa-robot"></i><br>Autonomy
                        </button>
                    </div>
                    
                    <div class="tab-content">
                        <!-- Health Tab -->
                        <div class="tab-panel active" id="health-tab">
                            <div class="diagnostic-section">
                                <div class="diagnostic-title">
                                    <i class="fas fa-heartbeat"></i> Execution Health
                                </div>
                                <div class="diagnostic-grid">
                                    <div class="metric-card">
                                        <div class="metric-label">Status</div>
                                        <div class="metric-value" id="worker-status">ACTIVE</div>
                                    </div>
                                    <div class="metric-card">
                                        <div class="metric-label">Task Queue</div>
                                        <div class="metric-value" id="task-queue">0</div>
                                    </div>
                                    <div class="metric-card">
                                        <div class="metric-label">Uptime</div>
                                        <div class="metric-value" id="worker-uptime">--</div>
                                    </div>
                                    <div class="metric-card">
                                        <div class="metric-label">Run Frequency</div>
                                        <div class="metric-value" id="run-frequency">-- c/min</div>
                                    </div>
                                </div>
                                
                                <div class="chart-container">
                                    <div class="chart-title">Worker Cycles Per Minute</div>
                                    <canvas id="cyclesChart"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- Resources Tab -->
                        <div class="tab-panel" id="resources-tab">
                            <div class="diagnostic-section">
                                <div class="diagnostic-title">
                                    <i class="fas fa-tachometer-alt"></i> Resource Usage
                                </div>
                                
                                <div class="diagnostic-grid">
                                    <div class="metric-card">
                                        <div class="metric-label">CPU Load</div>
                                        <div class="gauge-container">
                                            <div class="gauge">
                                                <div class="gauge-value" id="cpu-gauge">0%</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="metric-card">
                                        <div class="metric-label">Memory Usage</div>
                                        <div class="gauge-container">
                                            <div class="gauge">
                                                <div class="gauge-value" id="memory-gauge">0MB</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="chart-container">
                                    <div class="chart-title">Resource Usage Over Time</div>
                                    <canvas id="resourceChart"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- Logs Tab -->
                        <div class="tab-panel" id="logs-tab">
                            <div class="diagnostic-section">
                                <div class="diagnostic-title">
                                    <i class="fas fa-file-alt"></i> Logs & Responses
                                </div>
                                
                                <div class="metric-card" style="margin-bottom: 15px;">
                                    <div class="metric-label">Last Task Log</div>
                                    <div class="log-container" id="last-task-log">
                                        <div class="log-entry">
                                            <span class="log-timestamp">12:34:56</span>
                                            <span>Worker initialized successfully</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="metric-card" style="margin-bottom: 15px;">
                                    <div class="metric-label">Recent Failed Runs</div>
                                    <div class="log-container" id="failed-runs">
                                        <div class="log-entry">No recent failures</div>
                                    </div>
                                </div>
                                
                                <div class="metric-card">
                                    <div class="metric-label">Handled Agents</div>
                                    <div class="log-container" id="handled-agents">
                                        <div class="log-entry success">
                                            <span class="log-timestamp">12:30:15</span>
                                            <span>AGENT-002 - Task completed</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Autonomy Tab -->
                        <div class="tab-panel" id="autonomy-tab">
                            <div class="diagnostic-section">
                                <div class="diagnostic-title">
                                    <i class="fas fa-robot"></i> Autonomy Diagnostics
                                </div>
                                
                                <div class="diagnostic-grid">
                                    <div class="metric-card">
                                        <div class="metric-label">Polling Status</div>
                                        <div class="metric-value" style="display: flex; align-items: center;">
                                            <div class="heartbeat" id="polling-indicator"></div>
                                            <span id="polling-status">ACTIVE</span>
                                        </div>
                                    </div>
                                    <div class="metric-card">
                                        <div class="metric-label">Custom Interval</div>
                                        <div class="metric-value" id="custom-interval">2000ms</div>
                                    </div>
                                    <div class="metric-card">
                                        <div class="metric-label">Backup Triggered</div>
                                        <div class="metric-value" id="backup-triggered">FALSE</div>
                                    </div>
                                    <div class="metric-card">
                                        <div class="metric-label">Auto Recovery</div>
                                        <div class="metric-value" id="auto-recovery">ENABLED</div>
                                    </div>
                                </div>
                                
                                <div class="chart-container">
                                    <div class="chart-title">Response Time Distribution</div>
                                    <canvas id="responseChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Enhanced Worker Data Structure with Diagnostics
        const workerData = [
            // Web Workers
            {
                name: "Quantum",
                path: 'webworker-house/worker-001',
                type: 'Web Worker',
                agent: 'AGENT-002',
                status: 'active',
                load: 42,
                memory: 78,
                tasks: 127,
                category: 'web',
                // Diagnostic Data
                diagnostics: {
                    uptime: Date.now() - Math.random() * 86400000, // Random uptime up to 24 hours
                    taskQueue: Math.floor(Math.random() * 10),
                    runFrequency: Math.floor(Math.random() * 60) + 20, // cycles per minute
                    isPolling: true,
                    customInterval: 2000,
                    backupTriggered: false,
                    autoRecovery: true,
                    lastTaskLog: `Task executed successfully at ${new Date().toLocaleTimeString()}`,
                    failedRuns: [],
                    handledAgents: ['AGENT-002'],
                    cyclesHistory: Array.from({length: 20}, () => Math.floor(Math.random() * 80) + 20),
                    resourceHistory: {
                        cpu: Array.from({length: 20}, () => Math.floor(Math.random() * 100)),
                        memory: Array.from({length: 20}, () => Math.floor(Math.random() * 200) + 50)
                    },
                    responseTimeHistory: Array.from({length: 10}, () => Math.floor(Math.random() * 500) + 50)
                }
            },
            // Additional workers with similar structure...
        ];

        // Generate diagnostic data for all workers
        const webWorkerNames = [
            "Cipher", "Byte", "Pixel", "Vector", "Tensor", "Matrix", "Nexion", "Prism", "Neuron", "Cortex",
            "Axiom", "Synapse", "Pulse", "Cobalt", "Astro", "Nova", "Stellar", "Nebula", "Orbit", "Cosmos",
            "Helios", "Luna", "Titan", "Quasar", "Meteor", "Comet", "Aurora", "Polaris", "Echo", "Sonic",
            "Cascade", "Dynamo", "Kinetic", "Hydro", "Fusion", "Tempest", "Tidal", "Phoenix", "Vortex", "Specter",
            "Phantom", "Shadow", "Eclipse", "Horizon", "Zenith", "Vertex", "Apex", "Nano", "Sigma", "Delta"
        ];

        const serviceWorkerNames = [
            "Guardian", "Sentinel", "Aegis", "Bastion", "Citadel", "Warden", "Keeper", "Protector", "Defender", "Shield"
        ];

        const supervisorNames = [
            "Overseer", "Director", "Commander", "Controller", "Administrator"
        ];
        
        // Worker avatars
        const workerAvatars = {
            'Web Worker': ['🌐', '💻', '⚡', '📱', '🔧', '🎯', '📊', '🔬', '⚙️', '🛠️'],
            'Service Worker': ['🛡️', '🔒', '⚙️', '🔧', '🛠️', '⚗️', '🔭', '📡', '🎛️', '🗃️'],
            'Supervisor': ['👑', '🎖️', '⭐', '🏆', '🎯', '👨‍💼', '👩‍💼', '🧠', '💎', '🔥']
        };

        // Generate full worker dataset
        for (let i = 2; i <= 200; i++) {
            const name = webWorkerNames[(i-2) % webWorkerNames.length] + (i > webWorkerNames.length + 1 ? `-${Math.floor(i/webWorkerNames.length)}` : '');
            const avatarIndex = Math.floor(Math.random() * workerAvatars['Web Worker'].length);
            workerData.push({
                name: name,
                path: `webworker-house/worker-${i.toString().padStart(3, '0')}`,
                type: 'Web Worker',
                agent: `AGENT-${(i+1).toString().padStart(3, '0')}`,
                status: Math.random() > 0.3 ? 'active' : 'idle',
                load: Math.floor(Math.random() * 60) + 5,
                memory: Math.floor(Math.random() * 50) + 30,
                tasks: Math.floor(Math.random() * 100) + 50,
                category: 'web',
                avatar: workerAvatars['Web Worker'][avatarIndex],
                description: `${name} is a Web Worker responsible for handling asynchronous operations and parallel processing tasks.`,
                tools: ['WebSockets', 'Fetch API', 'IndexedDB', 'Web Crypto', 'Canvas API', 'WebGL', 'Web Audio'].sort(() => Math.random() - 0.5).slice(0, 2 + Math.floor(Math.random() * 3)),
                diagnostics: generateDiagnosticData()
            });
        }

        for (let i = 1; i <= 40; i++) {
            const name = serviceWorkerNames[(i-1) % serviceWorkerNames.length] + (i > serviceWorkerNames.length ? `-${Math.floor(i/serviceWorkerNames.length)}` : '');
            const avatarIndex = Math.floor(Math.random() * workerAvatars['Service Worker'].length);
            workerData.push({
                name: name,
                path: `serviceworker-house/worker-${i.toString().padStart(3, '0')}`,
                type: 'Service Worker',
                agent: `AGENT-${(i+200).toString().padStart(3, '0')}`,
                status: Math.random() > 0.3 ? 'active' : 'idle',
                load: Math.floor(Math.random() * 60) + 5,
                memory: Math.floor(Math.random() * 50) + 30,
                tasks: Math.floor(Math.random() * 100) + 50,
                category: 'service',
                avatar: workerAvatars['Service Worker'][avatarIndex],
                description: `${name} is a Service Worker that manages network requests, offline caching, and background synchronization.`,
                tools: ['Cache API', 'Background Sync', 'Push API', 'Notifications', 'Intercept Fetch', 'IndexedDB', 'Offline First'].sort(() => Math.random() - 0.5).slice(0, 2 + Math.floor(Math.random() * 3)),
                diagnostics: generateDiagnosticData()
            });
        }

        for (let i = 1; i <= 15; i++) {
            const name = supervisorNames[(i-1) % supervisorNames.length] + (i > supervisorNames.length ? `-${Math.floor(i/supervisorNames.length)}` : '');
            const avatarIndex = Math.floor(Math.random() * workerAvatars['Supervisor'].length);
            workerData.push({
                name: name,
                path: `supervisor-house/worker-${i.toString().padStart(3, '0')}`,
                type: 'Supervisor',
                agent: `AGENT-${(i+240).toString().padStart(3, '0')}`,
                status: Math.random() > 0.3 ? 'active' : 'idle',
                load: Math.floor(Math.random() * 60) + 5,
                memory: Math.floor(Math.random() * 50) + 30,
                tasks: Math.floor(Math.random() * 100) + 50,
                category: 'supervisor',
                avatar: workerAvatars['Supervisor'][avatarIndex],
                description: `${name} is a Supervisor that oversees and coordinates operations between multiple workers and services.`,
                tools: ['Thread Management', 'Resource Allocation', 'Load Balancing', 'Error Handling', 'Worker Coordination', 'Health Monitoring', 'Performance Analysis'].sort(() => Math.random() - 0.5).slice(0, 3 + Math.floor(Math.random() * 2)),
                diagnostics: generateDiagnosticData()
            });
        }

        function generateDiagnosticData() {
            return {
                uptime: Date.now() - Math.random() * 86400000,
                taskQueue: Math.floor(Math.random() * 10),
                runFrequency: Math.floor(Math.random() * 60) + 20,
                isPolling: Math.random() > 0.1,
                customInterval: [1000, 2000, 5000][Math.floor(Math.random() * 3)],
                backupTriggered: Math.random() < 0.1,
                autoRecovery: Math.random() > 0.05,
                lastTaskLog: `Task executed at ${new Date(Date.now() - Math.random() * 3600000).toLocaleTimeString()}`,
                failedRuns: Math.random() < 0.3 ? [`Error: ${['Connection timeout', 'Memory overflow', 'Invalid input'][Math.floor(Math.random() * 3)]}`] : [],
                handledAgents: [`AGENT-${Math.floor(Math.random() * 255) + 1}`],
                cyclesHistory: Array.from({length: 20}, () => Math.floor(Math.random() * 80) + 20),
                resourceHistory: {
                    cpu: Array.from({length: 20}, () => Math.floor(Math.random() * 100)),
                    memory: Array.from({length: 20}, () => Math.floor(Math.random() * 200) + 50)
                },
                responseTimeHistory: Array.from({length: 10}, () => Math.floor(Math.random() * 500) + 50)
            };
        }

        let selectedWorkerIndex = 0;
        let currentCategory = "all";
        let charts = {};

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            createParticles();
            populateWorkerList();
            initEventListeners();
            initializeTabs();
            selectWorker(0);
            
            // Remove loading overlay completely from DOM
            const loadingOverlay = document.getElementById('loadingOverlay');
            if (loadingOverlay) {
                loadingOverlay.parentNode.removeChild(loadingOverlay);
            }
            
            setInterval(simulateSystemActivity, 2000);
        });

        function createParticles() {
            const container = document.getElementById('particles');
            if(!container) return;
            for (let i = 0; i < 20; i++) _createSingleParticle(container);
            setInterval(() => _createSingleParticle(container), 1000);
        }
        
        function _createSingleParticle(container) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDelay = Math.random() * 20 + 's';
            particle.style.animationDuration = (Math.random() * 10 + 15) + 's';
            container.appendChild(particle);
            setTimeout(() => particle.remove(), 25000);
        }

        function populateWorkerList() {
            const workerListEl = document.getElementById('workerList');
            workerListEl.innerHTML = '';
            
            let workersToShow = workerData;
            
            if (currentCategory !== 'all') {
                workersToShow = workerData.filter(worker => worker.category === currentCategory);
            }
            
            workersToShow.forEach((worker, index) => {
                const card = document.createElement('div');
                card.className = 'worker-card';
                card.dataset.index = index;
                
                // Use worker avatar if available, otherwise fallback to default icon
                let icon = worker.avatar || '💻';
                
                card.innerHTML = `
                    <div class="card-status-dot status-${worker.status}" id="dot-${worker.path.replace(/\//g, '-')}"></div>
                    <div class="card-avatar">${icon}</div>
                    <div class="card-name">${worker.name}</div>
                    <div class="card-type">${worker.type}</div>
                    <div class="card-id">${worker.path.split('/')[1]}</div>
                `;
                
                card.addEventListener('click', () => {
                    selectWorker(index, workersToShow);
                    showWorkerDetails(workersToShow[index]);
                });
                workerListEl.appendChild(card);
            });
        }

        function selectWorker(index, workers = null) {
            const workersToUse = workers || (currentCategory === 'all' ? 
                workerData : workerData.filter(worker => worker.category === currentCategory));
                
            if (index < 0 || index >= workersToUse.length) return;
            
            selectedWorkerIndex = index;
            const worker = workersToUse[index];
            
            // Update hologram
            document.getElementById('hologramName').textContent = worker.name;
            document.getElementById('hologramPath').textContent = worker.path;
            document.getElementById('hologramAgent').textContent = worker.agent;
            document.getElementById('hologramFunction').textContent = worker.type;

            // Update hologram appearance
            const hologramSilhouette = document.getElementById('hologramSilhouette');
            const hologramScanLine = document.getElementById('hologramScanLine');
            
            let colorClass = worker.status;
            if (worker.category === 'service' && worker.status !== 'idle') {
                colorClass = 'status-processing';
            } else if (worker.category === 'supervisor' && worker.status !== 'idle') {
                colorClass = 'status-active';
            }
            
            hologramSilhouette.className = `worker-silhouette ${colorClass}`;
            hologramScanLine.className = `worker-scan-line ${colorClass}`;

            // Update active card
            document.querySelectorAll('.worker-card').forEach((card, i) => {
                card.classList.toggle('active', i === index);
                if (i === index) {
                    card.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
                }
            });
            
            updateDiagnosticPanels(worker);
        }

        function updateDiagnosticPanels(worker) {
            const diag = worker.diagnostics;
            
            // Health metrics
            document.getElementById('worker-status').textContent = worker.status.toUpperCase();
            document.getElementById('task-queue').textContent = diag.taskQueue;
            document.getElementById('worker-uptime').textContent = formatUptime(Date.now() - diag.uptime);
            document.getElementById('run-frequency').textContent = `${diag.runFrequency} c/min`;
            
            // Resource gauges
            document.getElementById('cpu-gauge').textContent = `${worker.load}%`;
            document.getElementById('memory-gauge').textContent = `${worker.memory}MB`;
            
            // Logs
            document.getElementById('last-task-log').innerHTML = `
                <div class="log-entry">
                    <span class="log-timestamp">${new Date().toLocaleTimeString()}</span>
                    <span>${diag.lastTaskLog}</span>
                </div>
            `;
            
            document.getElementById('failed-runs').innerHTML = diag.failedRuns.length > 0 
                ? diag.failedRuns.map(error => `<div class="log-entry error">${error}</div>`).join('')
                : '<div class="log-entry">No recent failures</div>';
                
            document.getElementById('handled-agents').innerHTML = diag.handledAgents.map(agent => `
                <div class="log-entry success">
                    <span class="log-timestamp">${new Date().toLocaleTimeString()}</span>
                    <span>${agent} - Task completed</span>
                </div>
            `).join('');
            
            // Autonomy
            document.getElementById('polling-status').textContent = diag.isPolling ? 'ACTIVE' : 'INACTIVE';
            document.getElementById('polling-indicator').className = `heartbeat ${diag.isPolling ? '' : 'offline'}`;
            document.getElementById('custom-interval').textContent = `${diag.customInterval}ms`;
            document.getElementById('backup-triggered').textContent = diag.backupTriggered ? 'TRUE' : 'FALSE';
            document.getElementById('auto-recovery').textContent = diag.autoRecovery ? 'ENABLED' : 'DISABLED';
            
            // Update charts
            updateCharts(worker);
        }

        function updateCharts(worker) {
            const diag = worker.diagnostics;
            
            // Cycles chart
            if (charts.cycles) {
                charts.cycles.data.datasets[0].data = diag.cyclesHistory;
                charts.cycles.update();
            } else {
                initializeCyclesChart(diag.cyclesHistory);
            }
            
            // Resource chart
            if (charts.resources) {
                charts.resources.data.datasets[0].data = diag.resourceHistory.cpu;
                charts.resources.data.datasets[1].data = diag.resourceHistory.memory;
                charts.resources.update();
            } else {
                initializeResourceChart(diag.resourceHistory);
            }
            
            // Response chart
            if (charts.response) {
                charts.response.data.datasets[0].data = diag.responseTimeHistory;
                charts.response.update();
            } else {
                initializeResponseChart(diag.responseTimeHistory);
            }
        }

        function initializeCyclesChart(data) {
            const ctx = document.getElementById('cyclesChart').getContext('2d');
            charts.cycles = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: Array.from({length: data.length}, (_, i) => i),
                    datasets: [{
                        label: 'Cycles/min',
                        data: data,
                        borderColor: '#00f2ff',
                        backgroundColor: 'rgba(0, 242, 255, 0.1)',
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        x: { display: false },
                        y: {
                            beginAtZero: true,
                            ticks: { color: '#e0e7ff' },
                            grid: { color: 'rgba(224, 231, 255, 0.1)' }
                        }
                    }
                }
            });
        }

        function initializeResourceChart(data) {
            const ctx = document.getElementById('resourceChart').getContext('2d');
            charts.resources = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: Array.from({length: data.cpu.length}, (_, i) => i),
                    datasets: [{
                        label: 'CPU %',
                        data: data.cpu,
                        borderColor: '#06d6a0',
                        backgroundColor: 'rgba(6, 214, 160, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'Memory MB',
                        data: data.memory,
                        borderColor: '#9854ff',
                        backgroundColor: 'rgba(152, 84, 255, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { 
                            display: true,
                            labels: { color: '#e0e7ff' }
                        }
                    },
                    scales: {
                        x: { display: false },
                        y: {
                            beginAtZero: true,
                            ticks: { color: '#e0e7ff' },
                            grid: { color: 'rgba(224, 231, 255, 0.1)' }
                        }
                    }
                }
            });
        }

        function initializeResponseChart(data) {
            const ctx = document.getElementById('responseChart').getContext('2d');
            charts.response = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: Array.from({length: data.length}, (_, i) => `T${i+1}`),
                    datasets: [{
                        label: 'Response Time (ms)',
                        data: data,
                        backgroundColor: 'rgba(255, 42, 109, 0.6)',
                        borderColor: '#ff2a6d',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        x: {
                            ticks: { color: '#e0e7ff' },
                            grid: { color: 'rgba(224, 231, 255, 0.1)' }
                        },
                        y: {
                            beginAtZero: true,
                            ticks: { color: '#e0e7ff' },
                            grid: { color: 'rgba(224, 231, 255, 0.1)' }
                        }
                    }
                }
            });
        }

        function initializeTabs() {
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabPanels = document.querySelectorAll('.tab-panel');
            
            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const targetTab = button.dataset.tab;
                    
                    // Update active states
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabPanels.forEach(panel => panel.classList.remove('active'));
                    
                    button.classList.add('active');
                    document.getElementById(`${targetTab}-tab`).classList.add('active');
                });
            });
        }

        function formatUptime(ms) {
            const seconds = Math.floor(ms / 1000);
            const minutes = Math.floor(seconds / 60);
            const hours = Math.floor(minutes / 60);
            const days = Math.floor(hours / 24);
            
            if (days > 0) return `${days}d ${hours % 24}h`;
            if (hours > 0) return `${hours}h ${minutes % 60}m`;
            if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
            return `${seconds}s`;
        }

        function initEventListeners() {
            // Navigation buttons
            document.getElementById('prevBtn').addEventListener('click', () => {
                const workers = currentCategory === 'all' ? 
                    workerData : workerData.filter(worker => worker.category === currentCategory);
                const prevIndex = selectedWorkerIndex > 0 ? selectedWorkerIndex - 1 : workers.length - 1;
                selectWorker(prevIndex, workers);
            });
            
            document.getElementById('nextBtn').addEventListener('click', () => {
                const workers = currentCategory === 'all' ? 
                    workerData : workerData.filter(worker => worker.category === currentCategory);
                const nextIndex = selectedWorkerIndex < workers.length - 1 ? selectedWorkerIndex + 1 : 0;
                selectWorker(nextIndex, workers);
            });
            
            // Filter buttons
            document.getElementById('allWorkersBtn').addEventListener('click', () => {
                setCategory('all');
                toggleActiveButton('allWorkersBtn');
            });
            
            document.getElementById('webWorkersBtn').addEventListener('click', () => {
                setCategory('web');
                toggleActiveButton('webWorkersBtn');
            });
            
            document.getElementById('serviceWorkersBtn').addEventListener('click', () => {
                setCategory('service');
                toggleActiveButton('serviceWorkersBtn');
            });
            
            document.getElementById('supervisorsBtn').addEventListener('click', () => {
                setCategory('supervisor');
                toggleActiveButton('supervisorsBtn');
            });
            
            // Diagnostic button
            document.getElementById('runDiagnosticBtn').addEventListener('click', () => {
                runFullDiagnostic();
            });
            
            // Heartbeat check button
            document.getElementById('heartbeatCheckBtn').addEventListener('click', () => {
                runHeartbeatCheck();
            });
            
            // Modal controls
            document.getElementById('closeModal').addEventListener('click', hideWorkerDetails);
            document.getElementById('minimizeModal').addEventListener('click', minimizeWorkerDetails);
            document.getElementById('expandModal').addEventListener('click', toggleExpandWorkerDetails);
            document.getElementById('minimizedModal').addEventListener('click', maximizeWorkerDetails);
            
            // Make modal draggable
            makeDraggable(document.getElementById('workerDetailsModal'), document.getElementById('modalHeader'));
            
            // Keyboard shortcuts
            document.addEventListener('keydown', function(event) {
                if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') return;
                switch (event.key) {
                    case 'ArrowLeft': document.getElementById('prevBtn').click(); break;
                    case 'ArrowRight': document.getElementById('nextBtn').click(); break;
                    case 'Escape': hideWorkerDetails(); break;
                    case ' ':
                        event.preventDefault();
                        document.getElementById('runDiagnosticBtn').click();
                        break;
                }
            });
        }
        
        // Make an element draggable
        function makeDraggable(element, handle) {
            let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
            
            handle.onmousedown = dragMouseDown;
            
            function dragMouseDown(e) {
                e = e || window.event;
                e.preventDefault();
                
                // Add dragging class for visual feedback
                element.classList.add('dragging');
                
                // Get the mouse cursor position at startup
                pos3 = e.clientX;
                pos4 = e.clientY;
                document.onmouseup = closeDragElement;
                // Call a function whenever the cursor moves
                document.onmousemove = elementDrag;
            }
            
            function elementDrag(e) {
                e = e || window.event;
                e.preventDefault();
                // Calculate the new cursor position
                pos1 = pos3 - e.clientX;
                pos2 = pos4 - e.clientY;
                pos3 = e.clientX;
                pos4 = e.clientY;
                
                // Get viewport dimensions
                const viewportWidth = window.innerWidth;
                const viewportHeight = window.innerHeight;
                
                // Calculate new position
                let newTop = element.offsetTop - pos2;
                let newLeft = element.offsetLeft - pos1;
                
                // Prevent the modal from being moved completely off-screen
                newTop = Math.max(0, Math.min(viewportHeight - 100, newTop));
                newLeft = Math.max(-element.offsetWidth / 2, Math.min(viewportWidth - element.offsetWidth / 2, newLeft));
                
                // Set the element's new position
                element.style.top = newTop + "px";
                element.style.left = newLeft + "px";
                // Reset transform if it was set
                element.style.transform = 'none';
            }
            
            function closeDragElement() {
                // Stop moving when mouse button is released
                document.onmouseup = null;
                document.onmousemove = null;
                element.classList.remove('dragging');
            }
        }
        
        function setCategory(category) {
            currentCategory = category;
            populateWorkerList();
            selectWorker(0);
        }
        
        function toggleActiveButton(activeButtonId) {
            const buttons = ['allWorkersBtn', 'webWorkersBtn', 'serviceWorkersBtn', 'supervisorsBtn'];
            buttons.forEach(buttonId => {
                document.getElementById(buttonId).classList.toggle('primary', buttonId === activeButtonId);
            });
        }

        function runFullDiagnostic() {
            const workers = currentCategory === 'all' ? 
                workerData : workerData.filter(worker => worker.category === currentCategory);
            const currentWorker = workers[selectedWorkerIndex];
            
            // Instead of alert, show the worker details modal with diagnostic info
            showWorkerDetails(currentWorker);
        }
        
        function runHeartbeatCheck() {
            // Temporarily highlight all worker cards to show they're being checked
            const workerCards = document.querySelectorAll('.worker-card');
            workerCards.forEach(card => {
                card.style.boxShadow = '0 0 15px var(--chart-green)';
                card.style.borderColor = 'var(--chart-green)';
                
                setTimeout(() => {
                    card.style.boxShadow = '';
                    card.style.borderColor = '';
                }, 1000);
            });
            
            // Count workers by status
            const totalWorkers = workerData.length;
            const activeWorkers = workerData.filter(w => w.status === 'active').length;
            const idleWorkers = workerData.filter(w => w.status === 'idle').length;
            const errorWorkers = workerData.filter(w => w.status === 'error').length;
            
            // Show heartbeat results
            alert(`Agent Lee's System Heartbeat Check Complete\n\nTotal Workers: ${totalWorkers}\nActive: ${activeWorkers}\nIdle: ${idleWorkers}\nError: ${errorWorkers}\n\nAll workers in Agent Lee's network responding to heartbeat check.`);
            
            // Update status displays
            document.getElementById('globalStatusText').textContent = 'Heartbeat Verified';
            setTimeout(() => {
                document.getElementById('globalStatusText').textContent = 'All Systems Operational';
            }, 3000);
        }

        function simulateSystemActivity() {
            // Update worker data and diagnostics
            workerData.forEach(worker => {
                // Randomly change some worker statuses
                if (Math.random() < 0.02) { // 2% chance
                    worker.status = worker.status === 'active' ? 'idle' : 'active';
                    updateWorkerCardStatus(worker.path, worker.status);
                }
                
                // Update resource usage
                worker.load = Math.max(5, Math.min(95, worker.load + (Math.random() * 10 - 5)));
                worker.memory = Math.max(20, Math.min(200, worker.memory + (Math.random() * 20 - 10)));
                
                // Update diagnostic histories
                worker.diagnostics.cyclesHistory.push(Math.floor(Math.random() * 80) + 20);
                if (worker.diagnostics.cyclesHistory.length > 20) {
                    worker.diagnostics.cyclesHistory.shift();
                }
                
                worker.diagnostics.resourceHistory.cpu.push(worker.load);
                worker.diagnostics.resourceHistory.memory.push(worker.memory);
                if (worker.diagnostics.resourceHistory.cpu.length > 20) {
                    worker.diagnostics.resourceHistory.cpu.shift();
                    worker.diagnostics.resourceHistory.memory.shift();
                }
                
                // Occasionally add response time data
                if (Math.random() < 0.3) {
                    worker.diagnostics.responseTimeHistory.push(Math.floor(Math.random() * 500) + 50);
                    if (worker.diagnostics.responseTimeHistory.length > 10) {
                        worker.diagnostics.responseTimeHistory.shift();
                    }
                }
            });
            
            // Update UI for selected worker
            const workers = currentCategory === 'all' ? 
                workerData : workerData.filter(worker => worker.category === currentCategory);
            
            if (selectedWorkerIndex >= 0 && selectedWorkerIndex < workers.length) {
                updateDiagnosticPanels(workers[selectedWorkerIndex]);
            }
            
            // Update global status
            updateGlobalStatus();
        }

        function updateWorkerCardStatus(workerPath, status) {
            const dot = document.getElementById(`dot-${workerPath.replace(/\//g, '-')}`);
            if (dot) {
                dot.className = `card-status-dot status-${status}`;
            }
        }
        
        // Modal functions
        function showWorkerDetails(worker) {
            const modal = document.getElementById('workerDetailsModal');
            const modalContent = document.getElementById('workerDetailsContent');
            const modalTitle = document.getElementById('modalTitle');
            const minimizedTitle = document.getElementById('minimizedTitle');
            
            // Set modal title
            modalTitle.textContent = `${worker.name} Details`;
            minimizedTitle.textContent = `${worker.name} Details`;
            
            // Generate worker details content
            modalContent.innerHTML = `
                <div style="display: flex; gap: 20px; margin-bottom: 20px;">
                    <div style="flex: 1;">
                        <div style="font-size: 50px; text-align: center; margin-bottom: 10px;">${worker.avatar || '💻'}</div>
                        <h3 style="font-family: 'Orbitron', sans-serif; color: var(--primary-color); text-align: center;">${worker.name}</h3>
                        <p style="text-align: center; opacity: 0.8;">${worker.agent}</p>
                    </div>
                    
                    <div style="flex: 2;">
                        <div style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                            <h3 style="font-family: 'Orbitron', sans-serif; color: var(--primary-color); margin-bottom: 10px;">Description</h3>
                            <p>${worker.description || 'No description available.'}</p>
                        </div>
                        
                        <div style="display: flex; gap: 15px;">
                            <div style="flex: 1; background: rgba(0,0,0,0.3); padding: 15px; border-radius: 10px;">
                                <h4 style="color: var(--chart-blue);">Status</h4>
                                <p style="font-family: 'Orbitron', sans-serif; color: var(--chart-${worker.status === 'active' ? 'green' : worker.status === 'idle' ? 'blue' : worker.status === 'processing' ? 'yellow' : 'red'});">${worker.status.toUpperCase()}</p>
                            </div>
                            
                            <div style="flex: 1; background: rgba(0,0,0,0.3); padding: 15px; border-radius: 10px;">
                                <h4 style="color: var(--chart-blue);">Type</h4>
                                <p>${worker.type}</p>
                            </div>
                            
                            <div style="flex: 1; background: rgba(0,0,0,0.3); padding: 15px; border-radius: 10px;">
                                <h4 style="color: var(--chart-blue);">Path</h4>
                                <p>${worker.path}</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin-bottom: 20px;">
                    <div style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 10px;">
                        <h4 style="color: var(--chart-blue); margin-bottom: 10px;">CPU Usage</h4>
                        <div class="performance-bar">
                            <div class="performance-fill cpu" style="width: ${worker.load}%">${worker.load}%</div>
                        </div>
                    </div>
                    
                    <div style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 10px;">
                        <h4 style="color: var(--chart-blue); margin-bottom: 10px;">Memory Usage</h4>
                        <div class="performance-bar">
                            <div class="performance-fill memory" style="width: ${(worker.memory / 250 * 100).toFixed(0)}%">${worker.memory} MB</div>
                        </div>
                    </div>
                </div>
                
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
                    <div style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 10px;">
                        <h4 style="color: var(--chart-green); margin-bottom: 10px;">Available Tools</h4>
                        <div style="display: flex; flex-wrap: wrap; gap: 5px;">
                            ${(worker.tools || ['No tools available']).map(tool => 
                                `<span style="background: rgba(0, 242, 255, 0.1); color: var(--primary-color); padding: 5px 10px; border-radius: 15px; font-size: 12px; border: 1px solid var(--panel-border);">${tool}</span>`
                            ).join('')}
                        </div>
                    </div>
                    
                    <div style="background: rgba(0,0,0,0.3); padding: 15px; border-radius: 10px;">
                        <h4 style="color: var(--chart-yellow); margin-bottom: 10px;">Task History</h4>
                        <p>Total Tasks: ${worker.tasks || 'Unknown'}</p>
                        <p>Uptime: ${formatUptime(Date.now() - worker.diagnostics.uptime)}</p>
                        <p>Run Frequency: ${worker.diagnostics.runFrequency} c/min</p>
                    </div>
                </div>
                
                <div style="margin-top: 20px; background: rgba(0,0,0,0.3); padding: 15px; border-radius: 10px;">
                    <h3 style="font-family: 'Orbitron', sans-serif; color: var(--primary-color); margin-bottom: 10px;">Diagnostics</h3>
                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px;">
                        <div>
                            <h4 style="color: var(--chart-blue); margin-bottom: 5px;">Task Queue</h4>
                            <p>${worker.diagnostics.taskQueue}</p>
                        </div>
                        <div>
                            <h4 style="color: var(--chart-blue); margin-bottom: 5px;">Custom Interval</h4>
                            <p>${worker.diagnostics.customInterval}ms</p>
                        </div>
                        <div>
                            <h4 style="color: var(--chart-blue); margin-bottom: 5px;">Auto Recovery</h4>
                            <p>${worker.diagnostics.autoRecovery ? 'ENABLED' : 'DISABLED'}</p>
                        </div>
                    </div>
                </div>
            `;
            
            // Show modal
            modal.classList.add('active');
            document.getElementById('minimizedModal').classList.remove('active');
        }

        function hideWorkerDetails() {
            document.getElementById('workerDetailsModal').classList.remove('active');
            document.getElementById('minimizedModal').classList.remove('active');
        }

        function minimizeWorkerDetails() {
            document.getElementById('workerDetailsModal').classList.remove('active');
            document.getElementById('minimizedModal').classList.add('active');
        }

        function maximizeWorkerDetails() {
            document.getElementById('workerDetailsModal').classList.add('active');
            document.getElementById('minimizedModal').classList.remove('active');
        }

        function toggleExpandWorkerDetails() {
            const modal = document.getElementById('workerDetailsModal');
            modal.classList.toggle('modal-expanded');
            
            const expandBtn = document.getElementById('expandModal');
            if (modal.classList.contains('modal-expanded')) {
                expandBtn.innerHTML = '<i class="fas fa-compress"></i>';
            } else {
                expandBtn.innerHTML = '<i class="fas fa-expand"></i>';
            }
        }

        function updateGlobalStatus() {
            const globalIndicator = document.getElementById('globalStatusIndicator');
            const globalStatusText = document.getElementById('globalStatusText');
            
            const activeCount = workerData.filter(worker => worker.status === 'active').length;
            const errorCount = workerData.filter(worker => worker.status === 'error').length;
            const highLoadCount = workerData.filter(worker => worker.load > 80).length;
            
            document.getElementById('workerCountDisplay').textContent = 
                `${activeCount} Active / ${workerData.length} Total`;
            
            if (errorCount > 0) {
                globalIndicator.className = 'status-indicator error';
                globalStatusText.textContent = 'System Alert';
            } else if (highLoadCount > workerData.length * 0.2) {
                globalIndicator.className = 'status-indicator warning';
                globalStatusText.textContent = 'High System Load';
            } else {
                globalIndicator.className = 'status-indicator';
                globalStatusText.textContent = 'All Systems Operational';
            }
        }
    </script>
</body>
</html>