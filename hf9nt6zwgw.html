<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Neuro-Operational Matrix - Live Network Dashboard</title>
    <!-- Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r146/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.6.0/p5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <style>
        :root {
            --dark-blue: #001529;
            --medium-blue: #002c56;
            --light-blue: #0af;
            --neon-blue: #00eeff;
            --highlight: #3fd2f8;
            --success: #1bf7cd;
            --warning: #f7d31b;
            --danger: #f73a1b;
            --grid-color: rgba(0, 174, 255, 0.1);
            --grid-highlight: rgba(0, 174, 255, 0.4);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background-color: var(--dark-blue);
            color: white;
            overflow: hidden;
            position: relative;
            height: 100vh;
        }
        
        #grid-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }
        
        .container {
            display: grid;
            grid-template-columns: 1fr 3fr 1fr;
            grid-template-rows: auto 1fr 3fr 1fr;
            height: 100vh;
            padding: 20px;
            gap: 15px;
            position: relative;
            z-index: 1;
        }
        
        /* Top Banner */
        .top-banner {
            grid-column: 1 / -1;
            grid-row: 1;
            background: linear-gradient(135deg, rgba(0, 30, 60, 0.9), rgba(0, 50, 90, 0.9));
            border: 2px solid var(--neon-blue);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 0 25px rgba(0, 238, 255, 0.3);
            backdrop-filter: blur(10px);
        }
        
        .top-banner::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 238, 255, 0.3), transparent);
            animation: bannerSweep 4s linear infinite;
        }
        
        .banner-title {
            font-size: 2.5em;
            font-weight: bold;
            color: var(--neon-blue);
            text-shadow: 0 0 15px var(--neon-blue);
            text-transform: uppercase;
            letter-spacing: 3px;
            margin: 0;
            position: relative;
            z-index: 2;
        }
        
        .banner-subtitle {
            font-size: 1.2em;
            color: var(--highlight);
            margin-top: 5px;
            font-weight: 300;
            letter-spacing: 2px;
            position: relative;
            z-index: 2;
        }
        
        @keyframes bannerSweep {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        
        .panel {
            background: rgba(0, 30, 60, 0.7);
            border: 1px solid var(--light-blue);
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 0 15px rgba(0, 174, 255, 0.2);
            backdrop-filter: blur(5px);
            position: relative;
            overflow: hidden;
        }
        
        .panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--neon-blue), transparent);
            animation: scanline 3s linear infinite;
        }
        
        .panel-title {
            color: var(--neon-blue);
            font-size: 0.9em;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .live-indicator {
            color: var(--success);
            font-size: 0.7em;
            padding: 2px 8px;
            border-radius: 10px;
            background: rgba(27, 247, 205, 0.2);
            animation: pulse 2s infinite;
        }
        
        .chart-container {
            width: 100%;
            height: calc(100% - 25px);
            position: relative;
        }
        
        /* Brain LLM Section */
        #brain-container {
            grid-column: 2;
            grid-row: 2 / 3;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: visible;
            z-index: 2;
            background: transparent;
            border: none;
            box-shadow: none;
        }
        
        #llm-brain {
            width: 220px;
            height: 180px;
            position: relative;
        }
        
        #llm-brain svg {
            width: 100%;
            height: 100%;
            filter: drop-shadow(0 0 10px var(--neon-blue));
        }
        
        .brain-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 3em;
            font-weight: bold;
            color: var(--neon-blue);
            text-shadow: 0 0 10px var(--neon-blue);
            z-index: 3;
        }
        
        .neural-path {
            position: absolute;
            stroke: var(--neon-blue);
            stroke-width: 2;
            stroke-dasharray: 5;
            stroke-dashoffset: 1000;
            fill: none;
            filter: drop-shadow(0 0 3px var(--neon-blue));
            z-index: 1;
        }
        
        /* Tower Mailboxes */
        #towers-container {
            grid-column: 2;
            grid-row: 3;
            perspective: 1000px;
            display: flex;
            justify-content: space-around;
            align-items: center;
            background: transparent;
            border: none;
            box-shadow: none;
        }
        
        .tower {
            width: 18%;
            height: 80%;
            position: relative;
            transform-style: preserve-3d;
            transform: rotateX(15deg);
            transition: transform 0.3s ease;
        }
        
        .tower:hover {
            transform: rotateX(15deg) scale(1.05);
        }
        
        .tower-face {
            position: absolute;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-end;
            background: linear-gradient(to bottom, var(--medium-blue), var(--dark-blue));
            border: 1px solid var(--light-blue);
            border-radius: 8px;
            overflow: hidden;
            backface-visibility: hidden;
        }
        
        .tower-front {
            transform: translateZ(20px);
        }
        
        .tower-back {
            transform: rotateY(180deg) translateZ(20px);
        }
        
        .tower-left {
            transform: rotateY(-90deg) translateZ(20px);
            width: 40px;
        }
        
        .tower-right {
            transform: rotateY(90deg) translateZ(20px);
            width: 40px;
        }
        
        .tower-top {
            transform: rotateX(90deg) translateZ(20px);
            height: 40px;
        }
        
        .tower-bottom {
            transform: rotateX(-90deg) translateZ(calc(100% - 20px));
            height: 40px;
        }
        
        .tower-title {
            color: var(--neon-blue);
            font-size: 1.2em;
            font-weight: bold;
            text-align: center;
            margin-bottom: 10px;
        }
        
        .tower-count {
            color: white;
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 20px;
        }
        
        .mailbox-slots {
            width: 90%;
            height: 70%;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding-bottom: 20px;
            overflow: hidden;
        }
        
        .mailbox-row {
            width: 100%;
            display: flex;
            justify-content: space-around;
            margin-bottom: 10px;
        }
        
        .mailbox-slot {
            width: 25px;
            height: 15px;
            background: rgba(0, 174, 255, 0.2);
            border: 1px solid var(--light-blue);
            border-radius: 2px;
            transition: all 0.3s ease;
        }
        
        .mailbox-slot.active {
            background: var(--neon-blue);
            box-shadow: 0 0 10px var(--neon-blue);
        }
        
        /* Diagnostic Panels */
        .diagnostics-overview {
            grid-column: 1;
            grid-row: 2;
        }
        
        .task-analytics {
            grid-column: 1;
            grid-row: 3 / 4;
        }
        
        .todo-list-panel {
            grid-column: 1;
            grid-row: 4;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .memory-stream-1 {
            grid-column: 3;
            grid-row: 2;
        }
        
        .task-workflow {
            grid-column: 3;
            grid-row: 3;
        }
        
        .shared-notepad {
            grid-column: 3;
            grid-row: 4;
        }
        
        .database-health {
            grid-column: 2;
            grid-row: 4;
        }
        
        #task-log-container {
            height: 90%;
            background: rgba(0, 30, 60, 0.7);
            border: 1px solid var(--light-blue);
            border-radius: 4px;
            padding: 10px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.8em;
            color: #aaf;
        }
        
        .log-entry {
            margin-bottom: 5px;
            border-bottom: 1px dotted rgba(0, 174, 255, 0.2);
            padding-bottom: 5px;
        }
        
        .log-timestamp {
            color: var(--highlight);
            margin-right: 5px;
        }
        
        .log-type-task {
            color: var(--success);
        }
        
        .log-type-error {
            color: var(--danger);
        }
        
        .log-type-info {
            color: var(--neon-blue);
        }
        
        /* To-Do List Styling */
        .todo-list {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }
        
        .todo-item {
            display: flex;
            align-items: center;
            padding: 8px 10px;
            border-radius: 4px;
            margin-bottom: 5px;
            background: rgba(0, 30, 60, 0.5);
            border-left: 3px solid var(--neon-blue);
            transition: all 0.3s ease;
        }
        
        .todo-item:hover {
            background: rgba(0, 30, 60, 0.8);
            transform: translateX(3px);
        }
        
        .todo-item.high-priority {
            border-left-color: var(--danger);
        }
        
        .todo-item.medium-priority {
            border-left-color: var(--warning);
        }
        
        .todo-item.low-priority {
            border-left-color: var(--success);
        }
        
        .todo-checkbox {
            margin-right: 10px;
            appearance: none;
            width: 16px;
            height: 16px;
            border: 1px solid var(--light-blue);
            border-radius: 3px;
            background: rgba(0, 174, 255, 0.1);
            position: relative;
            cursor: pointer;
        }
        
        .todo-checkbox:checked {
            background: var(--success);
        }
        
        .todo-checkbox:checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: var(--dark-blue);
            font-size: 12px;
            font-weight: bold;
        }
        
        .todo-text {
            flex-grow: 1;
            font-size: 0.85em;
        }
        
        .todo-badge {
            font-size: 0.7em;
            padding: 2px 6px;
            border-radius: 10px;
            margin-left: 8px;
            background: rgba(0, 174, 255, 0.2);
        }
        
        .todo-badge.llm {
            background: rgba(247, 211, 27, 0.2);
            color: var(--warning);
        }
        
        .todo-badge.agent {
            background: rgba(0, 238, 255, 0.2);
            color: var(--neon-blue);
        }
        
        .todo-badge.worker {
            background: rgba(27, 247, 205, 0.2);
            color: var(--success);
        }
        
        .todo-new {
            display: flex;
            margin-top: 10px;
        }
        
        .todo-input {
            flex-grow: 1;
            background: rgba(0, 30, 60, 0.5);
            border: 1px solid var(--light-blue);
            color: white;
            padding: 5px 10px;
            border-radius: 4px 0 0 4px;
            font-size: 0.85em;
        }
        
        .todo-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }
        
        .todo-add-btn {
            background: var(--neon-blue);
            color: var(--dark-blue);
            border: none;
            padding: 5px 10px;
            border-radius: 0 4px 4px 0;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .todo-add-btn:hover {
            background: var(--light-blue);
        }
        
        /* Shared Notepad */
        .shared-notepad-textarea {
            width: 100%;
            height: calc(100% - 60px);
            background: rgba(0, 30, 60, 0.5);
            border: 1px solid var(--light-blue);
            border-radius: 4px;
            color: white;
            padding: 10px;
            font-family: monospace;
            font-size: 0.9em;
            resize: none;
        }
        
        .shared-notepad-stats {
            display: flex;
            justify-content: space-between;
            margin-top: 8px;
            font-size: 0.75em;
            color: rgba(255, 255, 255, 0.7);
        }
        
        .notepad-stat {
            display: flex;
            align-items: center;
        }
        
        .notepad-stat-icon {
            margin-right: 5px;
        }
        
        .notepad-stat-value {
            color: var(--neon-blue);
        }
        
        /* Neural Connections */
        .neural-connection {
            position: absolute;
            background: linear-gradient(90deg, transparent, var(--neon-blue), transparent);
            height: 2px;
            opacity: 0.7;
            filter: blur(1px);
            z-index: -1;
            transform-origin: left center;
        }
        
        .data-packet {
            position: absolute;
            width: 10px;
            height: 10px;
            background: var(--neon-blue);
            border-radius: 50%;
            box-shadow: 0 0 10px var(--neon-blue);
            z-index: 10;
        }
        
        /* Task Overlay */
        .task-overlay {
            position: absolute;
            background: rgba(0, 30, 60, 0.7);
            border: 1px solid var(--neon-blue);
            border-radius: 4px;
            padding: 8px 12px;
            color: white;
            font-size: 0.9em;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
            backdrop-filter: blur(5px);
            z-index: 100;
            box-shadow: 0 0 15px rgba(0, 174, 255, 0.4);
        }
        
        /* Animations */
        @keyframes scanline {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @keyframes neuralPulse {
            0% { stroke-dashoffset: 1000; }
            100% { stroke-dashoffset: 0; }
        }
        
        /* Control Panel */
        .control-panel {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 30, 60, 0.8);
            border: 1px solid var(--light-blue);
            border-radius: 20px;
            padding: 8px 15px;
            display: flex;
            gap: 15px;
            backdrop-filter: blur(5px);
            z-index: 1000;
        }
        
        .control-btn {
            background: none;
            border: 1px solid var(--light-blue);
            color: var(--neon-blue);
            padding: 5px 10px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 0.8em;
            transition: all 0.3s ease;
        }
        
        .control-btn:hover {
            background: rgba(0, 174, 255, 0.2);
            box-shadow: 0 0 10px rgba(0, 174, 255, 0.4);
        }
        
        .control-dropdown {
            background: rgba(0, 30, 60, 0.8);
            border: 1px solid var(--light-blue);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
        }
        
        /* Task Pulse Effect */
        .task-pulse {
            position: absolute;
            width: 30px;
            height: 30px;
            background: transparent;
            border: 2px solid var(--neon-blue);
            border-radius: 50%;
            opacity: 1;
            pointer-events: none;
            z-index: 9999;
        }
        
        /* Database Connection Effect */
        .db-connection {
            position: absolute;
            height: 2px;
            background: linear-gradient(90deg, var(--neon-blue), var(--success));
            opacity: 0.8;
            z-index: 5;
        }
        
        .db-packet {
            position: absolute;
            width: 8px;
            height: 8px;
            background: var(--success);
            border-radius: 50%;
            box-shadow: 0 0 8px var(--success);
            z-index: 6;
            animation: movePacket 3s linear infinite;
        }
        
        @keyframes movePacket {
            0% { transform: translateX(0); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateX(100%); opacity: 0; }
        }
    </style>
</head>
<body>
    <!-- Grid Background -->
    <canvas id="grid-background"></canvas>
    
    <div class="container">
        <!-- Top Banner -->
        <div class="top-banner">
            <h1 class="banner-title">Agent Lee's Dynamic To-Do List</h1>
            <p class="banner-subtitle">and Shared Notepad</p>
        </div>
        
        <!-- Brain (LLM) - Central Neural Core -->
        <div id="brain-container">
            <div id="llm-brain">
                <div class="brain-text">LLM</div>
                <!-- Brain SVG with neural network paths will be added by JS -->
            </div>
        </div>
        
        <!-- Towers - Mailbox Data Routers -->
        <div id="towers-container">
            <div class="tower" id="ai-agents-tower">
                <div class="tower-face tower-front">
                    <div class="tower-title">AI<br>AGENTS</div>
                    <div class="tower-count">125</div>
                    <div class="mailbox-slots" id="ai-agents-slots">
                        <!-- Slots will be generated by JS -->
                    </div>
                </div>
                <div class="tower-face tower-back"></div>
                <div class="tower-face tower-left"></div>
                <div class="tower-face tower-right"></div>
                <div class="tower-face tower-top"></div>
                <div class="tower-face tower-bottom"></div>
            </div>
            
            <div class="tower" id="web-workers-tower">
                <div class="tower-face tower-front">
                    <div class="tower-title">WEB<br>WORKERS</div>
                    <div class="tower-count">200</div>
                    <div class="mailbox-slots" id="web-workers-slots">
                        <!-- Slots will be generated by JS -->
                    </div>
                </div>
                <div class="tower-face tower-back"></div>
                <div class="tower-face tower-left"></div>
                <div class="tower-face tower-right"></div>
                <div class="tower-face tower-top"></div>
                <div class="tower-face tower-bottom"></div>
            </div>
            
            <div class="tower" id="service-workers-tower">
                <div class="tower-face tower-front">
                    <div class="tower-title">SERVICE<br>WORKERS</div>
                    <div class="tower-count">400</div>
                    <div class="mailbox-slots" id="service-workers-slots">
                        <!-- Slots will be generated by JS -->
                    </div>
                </div>
                <div class="tower-face tower-back"></div>
                <div class="tower-face tower-left"></div>
                <div class="tower-face tower-right"></div>
                <div class="tower-face tower-top"></div>
                <div class="tower-face tower-bottom"></div>
            </div>
            
            <div class="tower" id="specialized-workers-tower">
                <div class="tower-face tower-front">
                    <div class="tower-title">SPECIALIZED<br>WORKERS</div>
                    <div class="tower-count">15</div>
                    <div class="mailbox-slots" id="specialized-workers-slots">
                        <!-- Slots will be generated by JS -->
                    </div>
                </div>
                <div class="tower-face tower-back"></div>
                <div class="tower-face tower-left"></div>
                <div class="tower-face tower-right"></div>
                <div class="tower-face tower-top"></div>
                <div class="tower-face tower-bottom"></div>
            </div>
        </div>
        
        <!-- Diagnostics Overview -->
        <div class="panel diagnostics-overview">
            <div class="panel-title">
                DIAGNOSTICS OVERVIEW
                <span class="live-indicator">LIVE</span>
            </div>
            <div class="chart-container">
                <canvas id="diagnostics-chart"></canvas>
            </div>
        </div>
        
        <!-- Task Analytics -->
        <div class="panel task-analytics">
            <div class="panel-title">
                TASK ANALYTICS
                <span class="live-indicator">LIVE</span>
            </div>
            <div class="chart-container">
                <canvas id="task-analytics-chart"></canvas>
            </div>
        </div>
        
        <!-- To-Do List Panel -->
        <div class="panel todo-list-panel">
            <div class="panel-title">
                AGENT TO-DO LIST MANAGER
                <span class="live-indicator">LIVE</span>
            </div>
            <ul class="todo-list" id="todo-list">
                <li class="todo-item high-priority">
                    <input type="checkbox" class="todo-checkbox">
                    <span class="todo-text">Diagnose failing LLM neural pathways</span>
                    <span class="todo-badge llm">LLM</span>
                </li>
                <li class="todo-item medium-priority">
                    <input type="checkbox" class="todo-checkbox">
                    <span class="todo-text">Verify agent plan integrity</span>
                    <span class="todo-badge agent">AGENT</span>
                </li>
                <li class="todo-item low-priority">
                    <input type="checkbox" class="todo-checkbox">
                    <span class="todo-text">Cache vector store in memory</span>
                    <span class="todo-badge worker">WORKER</span>
                </li>
                <li class="todo-item high-priority">
                    <input type="checkbox" class="todo-checkbox">
                    <span class="todo-text">Validate LLM responses</span>
                    <span class="todo-badge agent">AGENT</span>
                </li>
                <li class="todo-item medium-priority">
                    <input type="checkbox" class="todo-checkbox">
                    <span class="todo-text">Monitor queue latency for Web Workers</span>
                    <span class="todo-badge worker">WORKER</span>
                </li>
            </ul>
            <div class="todo-new">
                <input type="text" class="todo-input" id="new-todo-input" placeholder="Add new task...">
                <button class="todo-add-btn" id="add-todo-btn">+</button>
            </div>
        </div>
        
        <!-- Memory Stream 1 -->
        <div class="panel memory-stream-1">
            <div class="panel-title">
                MEMORY STREAM
                <span class="live-indicator">LIVE</span>
            </div>
            <div class="chart-container">
                <canvas id="memory-stream-1-chart"></canvas>
            </div>
        </div>
        
        <!-- Task Workflow -->
        <div class="panel task-workflow">
            <div class="panel-title">
                TASK WORKFLOW
                <span class="live-indicator">LIVE</span>
            </div>
            <div class="chart-container">
                <canvas id="task-workflow-chart"></canvas>
            </div>
        </div>
        
        <!-- Shared Notepad -->
        <div class="panel shared-notepad">
            <div class="panel-title">
                SHARED NOTEPAD & DATABASE SYNC
                <span class="live-indicator">LIVE</span>
            </div>
            <textarea id="shared-notepad" class="shared-notepad-textarea" placeholder="Agent Lee is listening... Type notes here to share across all network nodes."></textarea>
            <div class="shared-notepad-stats">
                <div class="notepad-stat">
                    <span class="notepad-stat-icon">📡</span>
                    <span>Connected: <span class="notepad-stat-value" id="notepad-connected">5/5</span></span>
                </div>
                <div class="notepad-stat">
                    <span class="notepad-stat-icon">🔄</span>
                    <span>Last Sync: <span class="notepad-stat-value" id="notepad-last-sync">Now</span></span>
                </div>
                <div class="notepad-stat">
                    <span class="notepad-stat-icon">💾</span>
                    <span>DB Mirror: <span class="notepad-stat-value" id="notepad-mirror">96%</span></span>
                </div>
            </div>
        </div>
        
        <!-- Database Health -->
        <div class="panel database-health">
            <div class="panel-title">
                DATABASE HEALTH & TO-DO TASKS
                <span class="live-indicator">LIVE</span>
            </div>
            <div class="chart-container">
                <canvas id="database-health-chart"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Task Overlay - will be moved around by JS -->
    <div class="task-overlay" id="task-overlay"></div>
    
    <!-- Control Panel -->
    <div class="control-panel">
        <select class="control-dropdown" id="filter-dropdown">
            <option value="all">All Components</option>
            <option value="ai-agents">Active AI Agents</option>
            <option value="web-workers">Web Workers</option>
            <option value="service-workers">Service Workers</option>
            <option value="specialized">Specialized Workers</option>
            <option value="errors">Error States</option>
        </select>
        
        <button class="control-btn" id="zoom-toggle">Enable Zoom</button>
        
        <select class="control-dropdown" id="playback-speed">
            <option value="1">Playback: 1x</option>
            <option value="5">Playback: 5x</option>
            <option value="10">Playback: 10x</option>
        </select>
        
        <button class="control-btn" id="replay-btn">↻ Replay Last 10 Min</button>
    </div>    <!-- JavaScript - Neuro-Operational Matrix Engine -->
    <script>
        // Global State Management
        const globalState = {
            chartInstances: [],
            towerStates: {
                'ai-agents': { active: 85, total: 125, activity: [] },
                'web-workers': { active: 156, total: 200, activity: [] },
                'service-workers': { active: 278, total: 400, activity: [] },
                'specialized-workers': { active: 12, total: 15, activity: [] }
            },
            neuralConnections: [],
            dataPackets: [],
            systemMetrics: {
                cpuUsage: 67,
                memoryUsage: 73,
                networkLatency: 45,
                taskThroughput: 342
            },
            taskLog: [],
            neuralPathAnimations: [],
            todoItems: [],
            sharedNotepad: {
                content: '',
                lastSync: new Date(),
                connected: 5,
                mirrorPercentage: 96
            }
        };

        // Local Storage Keys
        const STORAGE_KEYS = {
            notepad: 'sharedNotepadMemory',
            tasks: 'sharedToDoTasks',
            nodes: 'pentagonBrainNodes',
            metrics: 'systemMetrics'
        };

        // Grid Background Animation
        function initGridBackground() {
            const canvas = document.getElementById('grid-background');
            const ctx = canvas.getContext('2d');
            
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            
            const gridSize = 50;
            let animationPhase = 0;
            
            function drawGrid() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // Base grid
                ctx.strokeStyle = 'rgba(0, 174, 255, 0.1)';
                ctx.lineWidth = 1;
                
                for (let x = 0; x <= canvas.width; x += gridSize) {
                    ctx.beginPath();
                    ctx.moveTo(x, 0);
                    ctx.lineTo(x, canvas.height);
                    ctx.stroke();
                }
                
                for (let y = 0; y <= canvas.height; y += gridSize) {
                    ctx.beginPath();
                    ctx.moveTo(0, y);
                    ctx.lineTo(canvas.width, y);
                    ctx.stroke();
                }
                
                // Animated highlight lines
                ctx.strokeStyle = `rgba(0, 174, 255, ${0.3 + Math.sin(animationPhase) * 0.2})`;
                ctx.lineWidth = 2;
                
                const highlightX = (animationPhase * 50) % canvas.width;
                const highlightY = (animationPhase * 30) % canvas.height;
                
                ctx.beginPath();
                ctx.moveTo(highlightX, 0);
                ctx.lineTo(highlightX, canvas.height);
                ctx.stroke();
                
                ctx.beginPath();
                ctx.moveTo(0, highlightY);
                ctx.lineTo(canvas.width, highlightY);
                ctx.stroke();
                
                animationPhase += 0.01;
                requestAnimationFrame(drawGrid);
            }
            
            drawGrid();
        }

        // LLM Brain Neural Network
        function initBrainNeuralNetwork() {
            const brain = document.getElementById('llm-brain');
            const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            svg.style.position = 'absolute';
            svg.style.top = '0';
            svg.style.left = '0';
            svg.style.width = '100%';
            svg.style.height = '100%';
            svg.style.pointerEvents = 'none';
            svg.style.zIndex = '1';
            
            // Create brain outline
            const brainPath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            brainPath.setAttribute('d', 'M60,50 C30,30 10,60 20,90 C25,110 45,120 70,115 C90,125 120,120 140,110 C160,125 190,120 200,100 C210,80 190,50 170,40 C150,20 130,25 110,35 C90,25 70,30 60,50 Z');
            brainPath.setAttribute('stroke', '#0af');
            brainPath.setAttribute('stroke-width', '2');
            brainPath.setAttribute('fill', 'none');
            brainPath.style.filter = 'drop-shadow(0 0 5px #0af)';
            
            // Create neural pathways
            const neuralPaths = [
                'M80,60 Q100,40 120,60',
                'M70,80 Q90,70 110,80',
                'M90,100 Q110,90 130,100',
                'M60,70 Q80,50 100,70',
                'M110,50 Q130,40 150,50',
                'M130,70 Q150,60 170,70',
                'M140,90 Q160,80 180,90'
            ];
            
            svg.appendChild(brainPath);
            
            neuralPaths.forEach((path, index) => {
                const neuralPath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                neuralPath.setAttribute('d', path);
                neuralPath.setAttribute('stroke', '#00eeff');
                neuralPath.setAttribute('stroke-width', '1.5');
                neuralPath.setAttribute('fill', 'none');
                neuralPath.setAttribute('stroke-dasharray', '4 2');
                neuralPath.style.animation = `neuralPulse 2s infinite ${index * 0.3}s`;
                neuralPath.style.filter = 'drop-shadow(0 0 3px #00eeff)';
                svg.appendChild(neuralPath);
            });
            
            // Add connection nodes
            const nodes = [
                { x: 80, y: 60 }, { x: 120, y: 60 }, { x: 70, y: 80 },
                { x: 110, y: 80 }, { x: 90, y: 100 }, { x: 130, y: 100 },
                { x: 150, y: 50 }, { x: 170, y: 70 }
            ];
            
            nodes.forEach(node => {
                const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
                circle.setAttribute('cx', node.x);
                circle.setAttribute('cy', node.y);
                circle.setAttribute('r', '3');
                circle.setAttribute('fill', '#00eeff');
                circle.style.filter = 'drop-shadow(0 0 5px #00eeff)';
                circle.style.animation = 'pulse 1.5s infinite';
                svg.appendChild(circle);
            });
            
            brain.appendChild(svg);
        }

        // Tower Mailbox Animation
        function initTowerMailboxes() {
            Object.keys(globalState.towerStates).forEach(towerId => {
                const tower = globalState.towerStates[towerId];
                const slotsContainer = document.getElementById(`${towerId}-slots`);
                
                // Create mailbox slots grid
                const rows = 8;
                const cols = 4;
                
                for (let row = 0; row < rows; row++) {
                    const rowDiv = document.createElement('div');
                    rowDiv.className = 'mailbox-row';
                    
                    for (let col = 0; col < cols; col++) {
                        const slot = document.createElement('div');
                        slot.className = 'mailbox-slot';
                        slot.dataset.slotId = `${row}-${col}`;
                        rowDiv.appendChild(slot);
                    }
                    
                    slotsContainer.appendChild(rowDiv);
                }
                
                // Initialize random active slots
                updateTowerActivity(towerId);
            });
        }

        function updateTowerActivity(towerId) {
            const tower = globalState.towerStates[towerId];
            const slots = document.querySelectorAll(`#${towerId}-slots .mailbox-slot`);
            
            // Clear all active states
            slots.forEach(slot => slot.classList.remove('active'));
            
            // Randomly activate slots based on activity level
            const activeSlots = Math.floor((tower.active / tower.total) * slots.length);
            const indices = [...Array(slots.length).keys()].sort(() => Math.random() - 0.5);
            
            for (let i = 0; i < activeSlots; i++) {
                slots[indices[i]].classList.add('active');
            }
        }

        // Neural Connection Lines
        function createNeuralConnections() {
            const brainRect = document.getElementById('llm-brain').getBoundingClientRect();
            const towersContainer = document.getElementById('towers-container');
            const towers = towersContainer.querySelectorAll('.tower');
            
            const brainCenter = {
                x: brainRect.left + brainRect.width / 2,
                y: brainRect.top + brainRect.height / 2
            };
            
            towers.forEach((tower, index) => {
                const towerRect = tower.getBoundingClientRect();
                const towerCenter = {
                    x: towerRect.left + towerRect.width / 2,
                    y: towerRect.top + towerRect.height / 2
                };
                
                createAnimatedConnection(brainCenter, towerCenter, index);
            });
            
            // Create connection to shared notepad and database
            createDatabaseConnections();
        }

        function createAnimatedConnection(start, end, delay) {
            const connection = document.createElement('div');
            connection.className = 'neural-connection';
            
            const length = Math.sqrt(Math.pow(end.x - start.x, 2) + Math.pow(end.y - start.y, 2));
            const angle = Math.atan2(end.y - start.y, end.x - start.x) * 180 / Math.PI;
            
            connection.style.left = start.x + 'px';
            connection.style.top = start.y + 'px';
            connection.style.width = length + 'px';
            connection.style.transform = `rotate(${angle}deg)`;
            connection.style.animationDelay = `${delay * 0.5}s`;
            
            document.body.appendChild(connection);
            globalState.neuralConnections.push(connection);
        }

        // Database Connection Visualization
        function createDatabaseConnections() {
            const notepadRect = document.querySelector('.shared-notepad').getBoundingClientRect();
            const dbRect = document.querySelector('.database-health').getBoundingClientRect();
            
            const notepadCenter = {
                x: notepadRect.left + notepadRect.width / 2,
                y: notepadRect.top + notepadRect.height / 2
            };
            
            const dbCenter = {
                x: dbRect.left + dbRect.width / 2,
                y: dbRect.top + dbRect.height / 2
            };
            
            // Create DB connection line
            const connection = document.createElement('div');
            connection.className = 'db-connection';
            
            const length = Math.sqrt(Math.pow(dbCenter.x - notepadCenter.x, 2) + Math.pow(dbCenter.y - notepadCenter.y, 2));
            const angle = Math.atan2(dbCenter.y - notepadCenter.y, dbCenter.x - notepadCenter.x) * 180 / Math.PI;
            
            connection.style.left = notepadCenter.x + 'px';
            connection.style.top = notepadCenter.y + 'px';
            connection.style.width = length + 'px';
            connection.style.transform = `rotate(${angle}deg)`;
            
            document.body.appendChild(connection);
            
            // Add moving data packets
            setInterval(() => {
                const packet = document.createElement('div');
                packet.className = 'db-packet';
                packet.style.left = notepadCenter.x + 'px';
                packet.style.top = notepadCenter.y + 'px';
                packet.style.width = length + 'px';
                packet.style.transform = `rotate(${angle}deg)`;
                
                document.body.appendChild(packet);
                
                setTimeout(() => {
                    packet.remove();
                }, 3000);
            }, 5000);
        }

        // Data Packet Animation
        function createDataPacket(start, end, type) {
            const packet = document.createElement('div');
            packet.className = 'data-packet';
            packet.style.left = start.x + 'px';
            packet.style.top = start.y + 'px';
            
            // Different colors for different data types
            const colors = {
                task: '#00eeff',
                response: '#1bf7cd',
                error: '#f73a1b',
                status: '#f7d31b'
            };
            
            packet.style.background = colors[type] || colors.task;
            packet.style.boxShadow = `0 0 10px ${colors[type] || colors.task}`;
            
            document.body.appendChild(packet);
            
            // Animate packet movement
            anime({
                targets: packet,
                left: end.x,
                top: end.y,
                duration: 2000,
                easing: 'easeInOutQuad',
                complete: () => {
                    packet.remove();
                }
            });
            
            globalState.dataPackets.push(packet);
        }

        // Chart Initialization
        function initDiagnosticsChart() {
            const ctx = document.getElementById('diagnostics-chart').getContext('2d');
            const chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: Array.from({length: 20}, (_, i) => i),
                    datasets: [{
                        label: 'System Health',
                        data: Array.from({length: 20}, () => Math.random() * 40 + 60),
                        borderColor: '#00eeff',
                        backgroundColor: 'rgba(0, 238, 255, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        x: { display: false },
                        y: { 
                            display: false,
                            min: 0,
                            max: 100
                        }
                    },
                    elements: { point: { radius: 0 } }
                }
            });
            
            globalState.chartInstances.push(chart);
        }

        function initTaskAnalyticsChart() {
            const ctx = document.getElementById('task-analytics-chart').getContext('2d');
            const chart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Queued', 'Processing', 'Complete', 'Failed'],
                    datasets: [{
                        data: [45, 123, 892, 12],
                        backgroundColor: [
                            '#f7d31b',
                            '#00eeff', 
                            '#1bf7cd',
                            '#f73a1b'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        x: { 
                            ticks: { color: '#aaf', font: { size: 10 } },
                            grid: { display: false }
                        },
                        y: { 
                            ticks: { color: '#aaf', font: { size: 10 } },
                            grid: { color: 'rgba(0, 174, 255, 0.2)' }
                        }
                    }
                }
            });
            
            globalState.chartInstances.push(chart);
        }

        function initMemoryStreamChart(canvasId) {
            const ctx = document.getElementById(canvasId).getContext('2d');
            const chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: Array.from({length: 50}, (_, i) => i),
                    datasets: [{
                        data: Array.from({length: 50}, () => Math.random() * 60 + 20),
                        borderColor: '#1bf7cd',
                        backgroundColor: 'rgba(27, 247, 205, 0.1)',
                        borderWidth: 1.5,
                        fill: true,
                        tension: 0.6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        x: { display: false },
                        y: { display: false }
                    },
                    elements: { point: { radius: 0 } }
                }
            });
            
            globalState.chartInstances.push(chart);
        }

        function initTaskWorkflowChart() {
            const ctx = document.getElementById('task-workflow-chart').getContext('2d');
            const chart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['LLM', 'Agents', 'Workers', 'Services'],
                    datasets: [{
                        data: [25, 35, 30, 10],
                        backgroundColor: [
                            '#f7d31b',
                            '#00eeff',
                            '#1bf7cd', 
                            '#f73a1b'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { 
                        legend: { 
                            display: true,
                            position: 'bottom',
                            labels: { 
                                color: '#aaf', 
                                font: { size: 8 },
                                usePointStyle: true
                            }
                        }
                    }
                }
            });
            
            globalState.chartInstances.push(chart);
        }

        function initDatabaseHealthChart() {
            const ctx = document.getElementById('database-health-chart').getContext('2d');
            const chart = new Chart(ctx, {
                type: 'polarArea',
                data: {
                    labels: ['Todo Tasks', 'Task History', 'Memory Cache', 'Log Storage'],
                    datasets: [{
                        data: [95, 87, 93, 89],
                        backgroundColor: [
                            'rgba(27, 247, 205, 0.7)',
                            'rgba(0, 238, 255, 0.7)',
                            'rgba(247, 211, 27, 0.7)',
                            'rgba(247, 58, 27, 0.7)'
                        ],
                        borderWidth: 2,
                        borderColor: '#00eeff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { 
                        legend: { 
                            display: true,
                            position: 'bottom',
                            labels: { 
                                color: '#aaf',
                                font: { size: 8 }
                            }
                        }
                    },
                    scales: {
                        r: {
                            ticks: { 
                                color: '#aaf',
                                font: { size: 8 },
                                backdropColor: 'transparent'
                            },
                            grid: { color: 'rgba(0, 174, 255, 0.2)' }
                        }
                    }
                }
            });
            
            globalState.chartInstances.push(chart);
        }

        // To-Do List Functionality
        function initToDoList() {
            const todoList = document.getElementById('todo-list');
            const todoInput = document.getElementById('new-todo-input');
            const addButton = document.getElementById('add-todo-btn');
            
            // Load tasks from localStorage
            loadTasksFromStorage();
            
            // Handle checkbox changes
            todoList.addEventListener('change', (e) => {
                if (e.target.classList.contains('todo-checkbox')) {
                    const todoItem = e.target.parentNode;
                    
                    if (e.target.checked) {
                        // Mark as completed
                        todoItem.style.opacity = '0.6';
                        todoItem.style.textDecoration = 'line-through';
                        
                        // Show animation
                        showTaskOverlay('Task completed!', e.clientX, e.clientY - 40);
                        
                        // Remove after delay
                        setTimeout(() => {
                            todoItem.remove();
                            saveTasksToStorage();
                        }, 5000);
                    } else {
                        todoItem.style.opacity = '1';
                        todoItem.style.textDecoration = 'none';
                    }
                }
            });
            
            // Add new task
            addButton.addEventListener('click', () => {
                addNewTask();
            });
            
            todoInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    addNewTask();
                }
            });
            
            function addNewTask() {
                const taskText = todoInput.value.trim();
                if (taskText) {
                    const priorityLevels = ['high', 'medium', 'low'];
                    const priority = priorityLevels[Math.floor(Math.random() * priorityLevels.length)];
                    
                    const sourceTypes = ['llm', 'agent', 'worker'];
                    const source = sourceTypes[Math.floor(Math.random() * sourceTypes.length)];
                    
                    const newItem = document.createElement('li');
                    newItem.className = `todo-item ${priority}-priority`;
                    
                    newItem.innerHTML = `
                        <input type="checkbox" class="todo-checkbox">
                        <span class="todo-text">${taskText}</span>
                        <span class="todo-badge ${source}">${source.toUpperCase()}</span>
                    `;
                    
                    todoList.prepend(newItem);
                    todoInput.value = '';
                    
                    // Show animation
                    showTaskOverlay('New task added!', addButton.getBoundingClientRect().right, addButton.getBoundingClientRect().top);
                    
                    // Save to storage
                    saveTasksToStorage();
                }
            }
            
            function loadTasksFromStorage() {
                const tasks = JSON.parse(localStorage.getItem(STORAGE_KEYS.tasks) || '[]');
                globalState.todoItems = tasks;
            }
            
            function saveTasksToStorage() {
                const tasks = [];
                document.querySelectorAll('.todo-item').forEach(item => {
                    const isChecked = item.querySelector('.todo-checkbox').checked;
                    const text = item.querySelector('.todo-text').textContent;
                    const priority = item.classList.contains('high-priority') ? 'high' : 
                                    item.classList.contains('medium-priority') ? 'medium' : 'low';
                    const sourceElement = item.querySelector('.todo-badge');
                    const source = sourceElement ? sourceElement.textContent.toLowerCase() : 'unknown';
                    
                    tasks.push({
                        text,
                        priority,
                        completed: isChecked,
                        source
                    });
                });
                
                globalState.todoItems = tasks;
                localStorage.setItem(STORAGE_KEYS.tasks, JSON.stringify(tasks));
            }
        }

        // Shared Notepad Functionality
        function initSharedNotepad() {
            const notepad = document.getElementById('shared-notepad');
            const notepadConnected = document.getElementById('notepad-connected');
            const notepadLastSync = document.getElementById('notepad-last-sync');
            const notepadMirror = document.getElementById('notepad-mirror');
            
            // Load content from localStorage
            notepad.value = localStorage.getItem(STORAGE_KEYS.notepad) || `🔷 Agent Lee's Task Memory Core initialized.
🧠 Pentagon Brain Network: 5 nodes connected.
📡 TaskMemoryBus: Active and monitoring.
💓 Vital Signs: All systems nominal.
🔄 Task Flow: Processing 14 active tasks.
🧠 Memory: State retention at 99.4%.
📞 Communication: Message rate 34.2/min.
⚙️ Autonomy: Decision triggers functioning.
📡 Dependencies: 5/6 APIs synchronized.
🚀 Latency: Response time 84ms average.
🗃️ Database: Connection stable, backup current.
📝 Notepad: Mirror sync verified at 96%.`;
            
            // Update sync stats
            updateNotepadStats();
            
            // Save on input
            notepad.addEventListener('input', () => {
                localStorage.setItem(STORAGE_KEYS.notepad, notepad.value);
                updateNotepadStats();
                simulateDataSync();
            });
            
            // Check for external changes
            window.addEventListener('storage', (e) => {
                if (e.key === STORAGE_KEYS.notepad && e.newValue !== notepad.value) {
                    notepad.value = e.newValue;
                    updateNotepadStats();
                }
            });
            
            function updateNotepadStats() {
                // Update last sync time
                globalState.sharedNotepad.lastSync = new Date();
                notepadLastSync.textContent = globalState.sharedNotepad.lastSync.toLocaleTimeString();
                
                // Simulate mirror percentage changes
                globalState.sharedNotepad.mirrorPercentage = Math.floor(Math.random() * 6 + 94); // 94-99%
                notepadMirror.textContent = `${globalState.sharedNotepad.mirrorPercentage}%`;
                
                // Simulate connection changes
                if (Math.random() > 0.9) {
                    globalState.sharedNotepad.connected = Math.floor(Math.random() * 2 + 4); // 4-5 connections
                } else {
                    globalState.sharedNotepad.connected = 5;
                }
                notepadConnected.textContent = `${globalState.sharedNotepad.connected}/5`;
            }
            
            function simulateDataSync() {
                const dbPanel = document.querySelector('.database-health');
                const notepadPanel = document.querySelector('.shared-notepad');
                
                const dbRect = dbPanel.getBoundingClientRect();
                const notepadRect = notepadPanel.getBoundingClientRect();
                
                const dbCenter = {
                    x: dbRect.left + dbRect.width / 2,
                    y: dbRect.top + dbRect.height / 2
                };
                
                const notepadCenter = {
                    x: notepadRect.left + notepadRect.width / 2,
                    y: notepadRect.top + notepadRect.height / 2
                };
                
                createDataPacket(notepadCenter, dbCenter, 'task');
                
                // Show sync overlay
                showTaskOverlay('Syncing notepad with database...', notepadCenter.x, notepadCenter.y - 40);
            }
            
            // Initial sync simulation
            setTimeout(() => {
                simulateDataSync();
            }, 2000);
        }

        // Task Overlay System
        function showTaskOverlay(message, x, y, duration = 3000) {
            const overlay = document.getElementById('task-overlay');
            overlay.textContent = message;
            overlay.style.left = x + 'px';
            overlay.style.top = y + 'px';
            overlay.style.opacity = '1';
            overlay.style.transform = 'translateY(0)';
            
            setTimeout(() => {
                overlay.style.opacity = '0';
                overlay.style.transform = 'translateY(20px)';
            }, duration);
        }

        // Live Task Routing Animation
        function simulateTaskRouting() {
            const brainRect = document.getElementById('llm-brain').getBoundingClientRect();
            const towers = document.querySelectorAll('.tower');
            const notepadRect = document.querySelector('.shared-notepad').getBoundingClientRect();
            const dbRect = document.querySelector('.database-health').getBoundingClientRect();
            
            setInterval(() => {
                // Random tower
                const randomTower = towers[Math.floor(Math.random() * towers.length)];
                const towerRect = randomTower.getBoundingClientRect();
                
                const brainCenter = {
                    x: brainRect.left + brainRect.width / 2,
                    y: brainRect.top + brainRect.height / 2
                };
                
                const towerCenter = {
                    x: towerRect.left + towerRect.width / 2,
                    y: towerRect.top + towerRect.height / 2
                };
                
                const notepadCenter = {
                    x: notepadRect.left + notepadRect.width / 2,
                    y: notepadRect.top + notepadRect.height / 2
                };
                
                const dbCenter = {
                    x: dbRect.left + dbRect.width / 2,
                    y: dbRect.top + dbRect.height / 2
                };
                
                // Create data packet
                const direction = Math.random() > 0.5;
                const start = direction ? brainCenter : towerCenter;
                const end = direction ? towerCenter : brainCenter;
                
                createDataPacket(start, end, 'task');
                
                // Sometimes also send task to database
                if (Math.random() > 0.7) {
                    setTimeout(() => {
                        createDataPacket(end, dbCenter, 'task');
                    }, 2000);
                }
                
                // Sometimes send tasks between notepad and database
                if (Math.random() > 0.8) {
                    setTimeout(() => {
                        const notepadToDb = Math.random() > 0.5;
                        createDataPacket(
                            notepadToDb ? notepadCenter : dbCenter,
                            notepadToDb ? dbCenter : notepadCenter,
                            'task'
                        );
                    }, 500);
                }
                
                // Show task overlay
                const towerId = randomTower.id.replace('-tower', '');
                const messages = [
                    `Dispatching task to ${towerId}`,
                    `Receiving response from ${towerId}`,
                    `Processing data in ${towerId}`,
                    `Task completed by ${towerId}`
                ];
                
                const message = messages[Math.floor(Math.random() * messages.length)];
                showTaskOverlay(message, start.x + 20, start.y - 30);
                
                // Update tower activity
                updateTowerActivity(towerId);
                
            }, 2000 + Math.random() * 3000);
        }

        // Chart Update Loop
        function startChartUpdates() {
            setInterval(() => {
                globalState.chartInstances.forEach(chart => {
                    if (chart.data.datasets[0].data) {
                        // Shift data for line charts
                        if (chart.config.type === 'line') {
                            chart.data.datasets[0].data.shift();
                            chart.data.datasets[0].data.push(Math.random() * 40 + 60);
                        }
                        // Update other chart types with random data
                        else if (chart.config.type === 'bar') {
                            chart.data.datasets[0].data = chart.data.datasets[0].data.map(() => 
                                Math.floor(Math.random() * 100 + 50)
                            );
                        }
                        else if (chart.config.type === 'radar') {
                            chart.data.datasets[0].data = chart.data.datasets[0].data.map(() => 
                                Math.floor(Math.random() * 30 + 70)
                            );
                        }
                        
                        chart.update('none');
                    }
                });
            }, 1000);
        }

        // Control Panel Events
        function initControlPanel() {
            const filterDropdown = document.getElementById('filter-dropdown');
            const zoomToggle = document.getElementById('zoom-toggle');
            const playbackSpeed = document.getElementById('playback-speed');
            const replayBtn = document.getElementById('replay-btn');
            
            filterDropdown.addEventListener('change', (e) => {
                // Filter visualization based on selection
                console.log('Filter changed to:', e.target.value);
            });
            
            zoomToggle.addEventListener('click', () => {
                // Toggle zoom functionality
                const isEnabled = zoomToggle.textContent.includes('Enable');
                zoomToggle.textContent = isEnabled ? 'Disable Zoom' : 'Enable Zoom';
            });
            
            playbackSpeed.addEventListener('change', (e) => {
                // Adjust animation speed
                console.log('Playback speed changed to:', e.target.value);
            });
            
            replayBtn.addEventListener('click', () => {
                // Replay last 10 minutes
                console.log('Replaying last 10 minutes...');
            });
        }

        // Initialization
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🧠 Initializing Neuro-Operational Matrix...');
            
            // Initialize components
            initGridBackground();
            initBrainNeuralNetwork();
            initTowerMailboxes();
            
            // Initialize charts
            initDiagnosticsChart();
            initTaskAnalyticsChart();
            initMemoryStreamChart('memory-stream-1-chart');
            initTaskWorkflowChart();
            initDatabaseHealthChart();
            
            // Initialize interactive components
            initToDoList();
            initSharedNotepad();
            initControlPanel();
            
            // Start animations
            setTimeout(() => {
                createNeuralConnections();
                simulateTaskRouting();
                startChartUpdates();
            }, 2000);
            
            console.log('✅ Neuro-Operational Matrix Online');
        });

        // Window resize handler
        window.addEventListener('resize', () => {
            const canvas = document.getElementById('grid-background');
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            
            // Recreate neural connections
            globalState.neuralConnections.forEach(conn => conn.remove());
            globalState.neuralConnections = [];
            setTimeout(createNeuralConnections, 500);
        });
    </script>
</body>
</html>